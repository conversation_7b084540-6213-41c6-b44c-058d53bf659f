<template>
  <div class="mcp-detail">
    <!-- 头部导航 -->
    <header class="header">
      <div class="container">
        <div class="header-content">
          <h1 class="logo" @click="$router.push('/')">MCP 广场</h1>
          <div class="header-actions">
            <button @click="$router.push('/')" class="btn btn-secondary">
              返回首页
            </button>
            <button v-if="isLoggedIn" @click="$router.push('/publish')" class="btn btn-primary">
              发布服务
            </button>
          </div>
        </div>
      </div>
    </header>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading">
      <div class="container">
        <p>加载中...</p>
      </div>
    </div>

    <!-- 服务详情 -->
    <main v-else-if="mcpService" class="main-content">
      <div class="container">
        <!-- 服务头部信息 -->
        <div class="service-header">
          <div class="service-info">
            <img :src="mcpService.icon" :alt="mcpService.name" class="service-icon" />
            <div class="service-meta">
              <h1 class="service-name">{{ mcpService.name }}</h1>
              <p class="service-author">by {{ mcpService.author }}</p>
              <div class="service-stats">
                <span class="stat">
                  <i class="icon-download"></i>
                  {{ mcpService.downloads }} 下载
                </span>
                <span class="stat">
                  <i class="icon-star"></i>
                  {{ mcpService.rating }} 评分
                </span>
                <span class="stat">
                  <i class="icon-calendar"></i>
                  更新于 {{ formatDate(mcpService.updatedAt) }}
                </span>
              </div>
            </div>
          </div>
          
          <div class="service-actions">
            <button @click="copyInstallCommand" class="btn btn-primary btn-large">
              <i class="icon-copy"></i>
              复制安装命令
            </button>
            <button @click="downloadConfig" class="btn btn-secondary">
              <i class="icon-download"></i>
              下载配置
            </button>
          </div>
        </div>

        <!-- 标签 -->
        <div class="service-tags">
          <span 
            v-for="tag in mcpService.tags" 
            :key="tag" 
            class="tag"
          >
            {{ tag }}
          </span>
        </div>

        <!-- 主要内容区域 -->
        <div class="content-grid">
          <!-- 左侧内容 -->
          <div class="main-column">
            <!-- 描述 -->
            <section class="content-section">
              <h2>服务描述</h2>
              <p class="service-description">{{ mcpService.description }}</p>
            </section>

            <!-- 安装说明 -->
            <section class="content-section">
              <h2>安装使用</h2>
              <div class="install-guide">
                <h3>1. 安装 MCP 服务</h3>
                <div class="code-block">
                  <code>{{ installCommand }}</code>
                  <button @click="copyInstallCommand" class="copy-btn">复制</button>
                </div>
                
                <h3>2. 配置 Claude Desktop</h3>
                <p>将以下配置添加到您的 Claude Desktop 配置文件中：</p>
                <div class="code-block">
                  <pre><code>{{ formattedConfig }}</code></pre>
                  <button @click="copyConfig" class="copy-btn">复制</button>
                </div>
                
                <h3>3. 重启 Claude Desktop</h3>
                <p>保存配置后，重启 Claude Desktop 即可使用该 MCP 服务。</p>
              </div>
            </section>

            <!-- 功能特性 -->
            <section v-if="mcpService.features" class="content-section">
              <h2>功能特性</h2>
              <ul class="features-list">
                <li v-for="feature in mcpService.features" :key="feature">
                  {{ feature }}
                </li>
              </ul>
            </section>

            <!-- 更新日志 -->
            <section v-if="mcpService.changelog" class="content-section">
              <h2>更新日志</h2>
              <div class="changelog">
                <div 
                  v-for="change in mcpService.changelog" 
                  :key="change.version"
                  class="changelog-item"
                >
                  <h4>{{ change.version }} - {{ formatDate(change.date) }}</h4>
                  <ul>
                    <li v-for="item in change.changes" :key="item">{{ item }}</li>
                  </ul>
                </div>
              </div>
            </section>
          </div>

          <!-- 右侧边栏 -->
          <div class="sidebar">
            <!-- 基本信息 -->
            <div class="info-card">
              <h3>基本信息</h3>
              <div class="info-item">
                <span class="label">版本:</span>
                <span class="value">{{ mcpService.version }}</span>
              </div>
              <div class="info-item">
                <span class="label">分类:</span>
                <span class="value">{{ getCategoryName(mcpService.category) }}</span>
              </div>
              <div class="info-item">
                <span class="label">许可证:</span>
                <span class="value">{{ mcpService.license || 'MIT' }}</span>
              </div>
              <div class="info-item">
                <span class="label">发布时间:</span>
                <span class="value">{{ formatDate(mcpService.createdAt) }}</span>
              </div>
            </div>

            <!-- 链接 -->
            <div class="info-card">
              <h3>相关链接</h3>
              <div class="links">
                <a 
                  v-if="mcpService.repository" 
                  :href="mcpService.repository" 
                  target="_blank" 
                  class="link-item"
                >
                  <i class="icon-github"></i>
                  源代码仓库
                </a>
                <a 
                  v-if="mcpService.documentation" 
                  :href="mcpService.documentation" 
                  target="_blank" 
                  class="link-item"
                >
                  <i class="icon-book"></i>
                  使用文档
                </a>
                <a 
                  v-if="mcpService.homepage" 
                  :href="mcpService.homepage" 
                  target="_blank" 
                  class="link-item"
                >
                  <i class="icon-home"></i>
                  项目主页
                </a>
              </div>
            </div>

            <!-- 作者信息 -->
            <div class="info-card">
              <h3>作者信息</h3>
              <div class="author-info">
                <img :src="mcpService.authorAvatar" :alt="mcpService.author" class="author-avatar" />
                <div>
                  <p class="author-name">{{ mcpService.author }}</p>
                  <p class="author-bio">{{ mcpService.authorBio }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- 未找到服务 -->
    <div v-else class="not-found">
      <div class="container">
        <h2>服务未找到</h2>
        <p>抱歉，您访问的 MCP 服务不存在或已被删除。</p>
        <button @click="$router.push('/')" class="btn btn-primary">
          返回首页
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

interface MCPService {
  id: string
  name: string
  description: string
  author: string
  authorBio: string
  authorAvatar: string
  icon: string
  tags: string[]
  downloads: number
  rating: number
  category: string
  version: string
  license: string
  createdAt: string
  updatedAt: string
  repository: string
  documentation: string
  homepage: string
  mcpConfig: string
  features?: string[]
  changelog?: Array<{
    version: string
    date: string
    changes: string[]
  }>
}

// 响应式数据
const mcpService = ref<MCPService | null>(null)
const loading = ref(true)

// 计算属性
const isLoggedIn = computed(() => {
  return !!localStorage.getItem('user_token')
})

const installCommand = computed(() => {
  if (!mcpService.value) return ''
  return `npm install -g ${mcpService.value.name}`
})

const formattedConfig = computed(() => {
  if (!mcpService.value) return ''
  try {
    const config = JSON.parse(mcpService.value.mcpConfig)
    return JSON.stringify(config, null, 2)
  } catch {
    return mcpService.value.mcpConfig
  }
})

// 方法
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

const getCategoryName = (category: string) => {
  const categoryMap: Record<string, string> = {
    'ai': 'AI 工具',
    'data': '数据处理',
    'api': 'API 集成',
    'utility': '实用工具'
  }
  return categoryMap[category] || category
}

const copyInstallCommand = async () => {
  try {
    await navigator.clipboard.writeText(installCommand.value)
    alert('安装命令已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
  }
}

const copyConfig = async () => {
  try {
    await navigator.clipboard.writeText(formattedConfig.value)
    alert('配置已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
  }
}

const downloadConfig = () => {
  if (!mcpService.value) return
  
  const blob = new Blob([formattedConfig.value], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `${mcpService.value.name}-config.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

// 加载服务详情
const loadMCPService = async () => {
  loading.value = true
  const serviceId = route.params.id as string
  
  // 模拟 API 调用
  setTimeout(() => {
    // 模拟数据
    if (serviceId === '1') {
      mcpService.value = {
        id: '1',
        name: 'Weather MCP',
        description: '提供全球天气信息查询服务，支持实时天气、天气预报、历史天气数据查询等功能。该服务集成了多个权威天气数据源，确保数据的准确性和实时性。',
        author: 'WeatherCorp',
        authorBio: '专注于气象数据服务的技术团队',
        authorAvatar: '/api/placeholder/48/48',
        icon: '/api/placeholder/64/64',
        tags: ['天气', 'API', '实用工具'],
        downloads: 1250,
        rating: 4.8,
        category: 'api',
        version: '1.2.0',
        license: 'MIT',
        createdAt: '2024-01-15',
        updatedAt: '2024-01-20',
        repository: 'https://github.com/weathercorp/weather-mcp',
        documentation: 'https://docs.weathercorp.com/mcp',
        homepage: 'https://weathercorp.com',
        mcpConfig: JSON.stringify({
          "name": "weather-mcp",
          "version": "1.2.0",
          "description": "Weather information service",
          "tools": [
            {
              "name": "get_weather",
              "description": "Get current weather for a location"
            },
            {
              "name": "get_forecast",
              "description": "Get weather forecast for a location"
            }
          ]
        }, null, 2),
        features: [
          '实时天气数据查询',
          '7天天气预报',
          '历史天气数据',
          '多种天气指标支持',
          '全球城市覆盖',
          '多语言支持'
        ],
        changelog: [
          {
            version: '1.2.0',
            date: '2024-01-20',
            changes: [
              '新增历史天气数据查询功能',
              '优化数据缓存机制',
              '修复部分城市数据不准确的问题'
            ]
          },
          {
            version: '1.1.0',
            date: '2024-01-18',
            changes: [
              '新增7天天气预报',
              '支持更多天气指标',
              '改进错误处理'
            ]
          }
        ]
      }
    } else {
      mcpService.value = null
    }
    loading.value = false
  }, 1000)
}

onMounted(() => {
  loadMCPService()
})
</script>

<style scoped>
.mcp-detail {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  background: white;
  border-bottom: 1px solid #e0e0e0;
  padding: 1rem 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-size: 1.5rem;
  font-weight: bold;
  color: #333;
  margin: 0;
  cursor: pointer;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-large {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #545b62;
}

.loading,
.not-found {
  padding: 4rem 0;
  text-align: center;
}

.main-content {
  padding: 2rem 0;
}

.service-header {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  margin-bottom: 1rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.service-info {
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
}

.service-icon {
  width: 80px;
  height: 80px;
  border-radius: 12px;
  background-color: #f0f0f0;
}

.service-name {
  font-size: 2rem;
  font-weight: bold;
  margin: 0 0 0.5rem 0;
  color: #333;
}

.service-author {
  font-size: 1.1rem;
  color: #666;
  margin: 0 0 1rem 0;
}

.service-stats {
  display: flex;
  gap: 1.5rem;
  font-size: 0.9rem;
  color: #666;
}

.stat {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.service-actions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.service-tags {
  background: white;
  border-radius: 8px;
  padding: 1rem 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag {
  background-color: #e9ecef;
  color: #495057;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.9rem;
}

.content-grid {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 2rem;
}

.content-section {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.content-section h2 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.3rem;
}

.service-description {
  line-height: 1.6;
  color: #555;
}

.install-guide h3 {
  margin: 1.5rem 0 0.5rem 0;
  color: #333;
}

.install-guide h3:first-child {
  margin-top: 0;
}

.code-block {
  position: relative;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 1rem;
  margin: 0.5rem 0;
  font-family: 'Courier New', monospace;
  overflow-x: auto;
}

.copy-btn {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  font-size: 0.8rem;
  cursor: pointer;
}

.features-list {
  list-style: none;
  padding: 0;
}

.features-list li {
  padding: 0.5rem 0;
  border-bottom: 1px solid #f0f0f0;
}

.features-list li:before {
  content: '✓';
  color: #28a745;
  font-weight: bold;
  margin-right: 0.5rem;
}

.changelog-item {
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #e0e0e0;
}

.changelog-item:last-child {
  border-bottom: none;
}

.changelog-item h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
}

.info-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.info-card h3 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.1rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.75rem;
}

.info-item:last-child {
  margin-bottom: 0;
}

.label {
  color: #666;
  font-weight: 500;
}

.value {
  color: #333;
}

.links {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.link-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #007bff;
  text-decoration: none;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.link-item:hover {
  background-color: #f8f9fa;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.author-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #f0f0f0;
}

.author-name {
  font-weight: 500;
  margin: 0 0 0.25rem 0;
  color: #333;
}

.author-bio {
  font-size: 0.9rem;
  color: #666;
  margin: 0;
}

@media (max-width: 768px) {
  .content-grid {
    grid-template-columns: 1fr;
  }
  
  .service-header {
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .service-actions {
    flex-direction: row;
    width: 100%;
  }
}
</style>
