# MCP 服务发布平台 API 接口文档

## 概述

本文档描述了 MCP 服务发布平台的后端 API 接口规范。所有接口均使用 RESTful 风格，数据格式为 JSON。

## 基础信息

- **Base URL**: `https://api.mcp-platform.com/v1`
- **认证方式**: <PERSON><PERSON> (JWT)
- **Content-Type**: `application/json`

## 认证接口

### 1. 用户登录

**POST** `/auth/login`

**请求体**:
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "user_123",
      "name": "用户名",
      "email": "<EMAIL>",
      "avatar": "https://example.com/avatar.jpg"
    }
  }
}
```

### 2. 用户注册

**POST** `/auth/register`

**请求体**:
```json
{
  "name": "用户名",
  "email": "<EMAIL>",
  "password": "password123"
}
```

### 3. OAuth 登录

**POST** `/auth/oauth/{provider}`

支持的 provider: `github`, `google`

**请求体**:
```json
{
  "code": "oauth_authorization_code",
  "redirect_uri": "https://mcp-platform.com/auth/callback"
}
```

## MCP 服务接口

### 1. 获取服务列表

**GET** `/mcp/services`

**查询参数**:
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 20)
- `category`: 分类筛选 (可选)
- `search`: 搜索关键词 (可选)
- `sort`: 排序方式 (`latest`, `popular`, `rating`)

**响应**:
```json
{
  "success": true,
  "data": {
    "services": [
      {
        "id": "service_123",
        "name": "Weather MCP",
        "description": "天气信息查询服务",
        "author": {
          "id": "user_123",
          "name": "WeatherCorp",
          "avatar": "https://example.com/avatar.jpg"
        },
        "icon": "https://example.com/icon.png",
        "tags": ["天气", "API"],
        "category": "api",
        "version": "1.2.0",
        "downloads": 1250,
        "rating": 4.8,
        "createdAt": "2024-01-15T00:00:00Z",
        "updatedAt": "2024-01-20T00:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "totalPages": 5
    }
  }
}
```

### 2. 获取服务详情

**GET** `/mcp/services/{id}`

**响应**:
```json
{
  "success": true,
  "data": {
    "id": "service_123",
    "name": "Weather MCP",
    "description": "详细的服务描述...",
    "author": {
      "id": "user_123",
      "name": "WeatherCorp",
      "bio": "专注于气象数据服务",
      "avatar": "https://example.com/avatar.jpg"
    },
    "icon": "https://example.com/icon.png",
    "tags": ["天气", "API"],
    "category": "api",
    "version": "1.2.0",
    "license": "MIT",
    "downloads": 1250,
    "rating": 4.8,
    "repository": "https://github.com/user/repo",
    "documentation": "https://docs.example.com",
    "homepage": "https://example.com",
    "mcpConfig": "{\"name\":\"weather-mcp\",\"version\":\"1.2.0\"}",
    "features": ["实时天气", "天气预报"],
    "changelog": [
      {
        "version": "1.2.0",
        "date": "2024-01-20T00:00:00Z",
        "changes": ["新增历史天气功能", "修复bug"]
      }
    ],
    "createdAt": "2024-01-15T00:00:00Z",
    "updatedAt": "2024-01-20T00:00:00Z"
  }
}
```

### 3. 发布服务

**POST** `/mcp/services`

**需要认证**: 是

**请求体**:
```json
{
  "name": "My MCP Service",
  "description": "服务描述",
  "category": "api",
  "version": "1.0.0",
  "tags": ["tag1", "tag2"],
  "repository": "https://github.com/user/repo",
  "documentation": "https://docs.example.com",
  "mcpConfig": "{\"name\":\"my-mcp\",\"version\":\"1.0.0\"}"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": "service_456",
    "message": "服务发布成功"
  }
}
```

### 4. 更新服务

**PUT** `/mcp/services/{id}`

**需要认证**: 是 (仅服务作者)

### 5. 删除服务

**DELETE** `/mcp/services/{id}`

**需要认证**: 是 (仅服务作者)

### 6. 上传服务图标

**POST** `/mcp/services/{id}/icon`

**需要认证**: 是

**请求体**: `multipart/form-data`
- `icon`: 图片文件 (PNG, JPG, SVG)

## 用户接口

### 1. 获取用户信息

**GET** `/users/profile`

**需要认证**: 是

### 2. 获取用户发布的服务

**GET** `/users/services`

**需要认证**: 是

### 3. 更新用户信息

**PUT** `/users/profile`

**需要认证**: 是

## 统计接口

### 1. 获取平台统计

**GET** `/stats/platform`

**响应**:
```json
{
  "success": true,
  "data": {
    "totalServices": 150,
    "totalDownloads": 50000,
    "totalUsers": 1200,
    "categories": {
      "ai": 45,
      "data": 30,
      "api": 50,
      "utility": 25
    }
  }
}
```

## 错误响应格式

所有错误响应都遵循以下格式:

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": "详细错误信息 (可选)"
  }
}
```

## 常见错误码

- `UNAUTHORIZED`: 未授权 (401)
- `FORBIDDEN`: 禁止访问 (403)
- `NOT_FOUND`: 资源不存在 (404)
- `VALIDATION_ERROR`: 参数验证失败 (400)
- `INTERNAL_ERROR`: 服务器内部错误 (500)

## 认证说明

需要认证的接口需要在请求头中包含:

```
Authorization: Bearer <token>
```

Token 通过登录接口获取，有效期为 7 天。

## 限流说明

- 未认证用户: 100 请求/小时
- 已认证用户: 1000 请求/小时
- 服务发布: 10 次/天

## 文件上传限制

- 图标文件: 最大 2MB
- 支持格式: PNG, JPG, JPEG, SVG
- 推荐尺寸: 64x64px 或 128x128px
