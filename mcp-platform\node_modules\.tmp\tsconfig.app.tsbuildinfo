{"root": ["../../env.d.ts", "../../src/app.vue", "../../src/main.ts", "../../src/components/helloworld.vue", "../../src/components/thewelcome.vue", "../../src/components/welcomeitem.vue", "../../src/components/icons/iconcommunity.vue", "../../src/components/icons/icondocumentation.vue", "../../src/components/icons/iconecosystem.vue", "../../src/components/icons/iconsupport.vue", "../../src/components/icons/icontooling.vue", "../../src/router/index.ts", "../../src/stores/counter.ts", "../../src/views/aboutview.vue", "../../src/views/homeview.vue", "../../src/views/login.vue", "../../src/views/mcpdetail.vue", "../../src/views/mcpmarketplace.vue", "../../src/views/mcppublish.vue"], "errors": true, "version": "5.8.3"}