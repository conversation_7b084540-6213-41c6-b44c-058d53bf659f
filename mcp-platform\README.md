# MCP 服务发布平台

基于 Vue 3 + Vite + TypeScript 构建的 MCP (Model Context Protocol) 服务发布和分享平台。

## 项目概述

这是一个专门用于发布和分享 MCP 服务的平台，用户可以：

- 🔍 **浏览服务**: 查看和搜索各种 MCP 服务
- 📤 **发布服务**: 登录后发布自己的 MCP 服务
- 📋 **快速使用**: 一键复制安装命令和配置
- 📊 **服务管理**: 管理已发布的服务

## 技术栈

- **前端框架**: Vue 3 + Composition API
- **构建工具**: Vite
- **语言**: TypeScript
- **路由**: Vue Router 4
- **状态管理**: Pinia
- **样式**: CSS3 + Flexbox/Grid
- **代码规范**: ESLint

## 项目结构

```
mcp-platform/
├── src/
│   ├── views/           # 页面组件
│   │   ├── MCPMarketplace.vue    # 服务市场页面
│   │   ├── MCPPublish.vue        # 服务发布页面
│   │   ├── MCPDetail.vue         # 服务详情页面
│   │   └── Login.vue             # 登录页面
│   ├── router/          # 路由配置
│   ├── stores/          # 状态管理
│   ├── components/      # 公共组件
│   ├── assets/          # 静态资源
│   └── main.ts          # 应用入口
├── API_DOCS.md          # API 接口文档
└── README.md            # 项目说明
```

## 功能特性

### 🏠 服务市场
- 服务列表展示
- 搜索和筛选功能
- 分类浏览
- 排序功能（最新、热门、评分）

### 📝 服务发布
- 完整的发布表单
- MCP 配置 JSON 编辑
- 图标上传
- 草稿保存

### 📖 服务详情
- 详细的服务信息
- 安装使用指南
- 配置文件下载
- 更新日志

### 🔐 用户系统
- 邮箱密码登录
- OAuth 登录（GitHub、Google）
- 用户注册
- 登录状态管理

## 快速开始

### 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

访问 http://localhost:5173

### 构建生产版本

```bash
npm run build
```

### 代码检查

```bash
npm run lint
```

## API 接口

后端 API 接口文档请参考 [API_DOCS.md](./API_DOCS.md)

主要接口包括：
- 用户认证（登录、注册、OAuth）
- MCP 服务管理（发布、查询、更新）
- 文件上传（图标）
- 统计数据

## 设计说明

### 架构特点
- 采用 Vue 3 + Vite 现代化架构
- 组件化开发，代码复用性高
- TypeScript 提供类型安全
- 响应式设计，支持移动端

### 用户体验
- 简洁直观的界面设计
- 流畅的交互体验
- 快速的服务发现和使用
- 完善的错误处理

### 安全考虑
- JWT Token 认证
- 路由守卫保护
- 输入验证和过滤
- XSS 防护

## 许可证

MIT License
