{"name": "mcp-platform", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "element-plus": "^2.10.3", "pinia": "^3.0.3", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@tsconfig/node18": "^18.2.4", "@types/node": "^18.19.50", "@vitejs/plugin-vue": "^4.6.2", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.1", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.28.0", "typescript": "~5.2.0", "vite": "^4.5.5", "vue-tsc": "^1.8.27"}}