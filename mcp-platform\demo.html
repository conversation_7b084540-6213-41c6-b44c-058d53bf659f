<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP 广场 - 演示页面</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background-color: #f5f5f5;
            line-height: 1.6;
        }

        .header {
            background: white;
            border-bottom: 1px solid #e0e0e0;
            padding: 1rem 0;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: #333;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background-color 0.2s;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background-color: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background-color: #0056b3;
        }

        .search-section {
            background: white;
            padding: 2rem 0;
            border-bottom: 1px solid #e0e0e0;
        }

        .search-bar {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .search-input {
            flex: 1;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
        }

        .search-btn {
            padding: 0.75rem 1.5rem;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .filters {
            display: flex;
            gap: 1rem;
        }

        .filter-select {
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }

        .main-content {
            padding: 2rem 0;
        }

        .mcp-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .mcp-card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .mcp-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .mcp-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .mcp-icon {
            width: 48px;
            height: 48px;
            border-radius: 8px;
            margin-right: 1rem;
            background: linear-gradient(45deg, #007bff, #0056b3);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .mcp-name {
            font-size: 1.1rem;
            font-weight: bold;
            margin: 0 0 0.25rem 0;
            color: #333;
        }

        .mcp-author {
            font-size: 0.9rem;
            color: #666;
            margin: 0;
        }

        .mcp-description {
            color: #555;
            line-height: 1.5;
            margin-bottom: 1rem;
        }

        .mcp-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .tag {
            background-color: #e9ecef;
            color: #495057;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
        }

        .mcp-stats {
            display: flex;
            justify-content: space-between;
            font-size: 0.9rem;
            color: #666;
        }

        .demo-notice {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 1rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .demo-notice h3 {
            color: #856404;
            margin-bottom: 0.5rem;
        }

        .demo-notice p {
            color: #856404;
            margin: 0;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }

            .search-bar {
                flex-direction: column;
            }

            .filters {
                flex-direction: column;
            }

            .mcp-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <h1 class="logo">MCP 广场</h1>
                <div class="header-actions">
                    <a href="#" class="btn btn-primary">登录</a>
                </div>
            </div>
        </div>
    </header>

    <!-- 搜索和筛选 -->
    <section class="search-section">
        <div class="container">
            <div class="search-bar">
                <input type="text" placeholder="搜索 MCP 服务..." class="search-input" />
                <button class="search-btn">搜索</button>
            </div>
            
            <div class="filters">
                <select class="filter-select">
                    <option value="">所有分类</option>
                    <option value="ai">AI 工具</option>
                    <option value="data">数据处理</option>
                    <option value="api">API 集成</option>
                    <option value="utility">实用工具</option>
                </select>
                
                <select class="filter-select">
                    <option value="latest">最新发布</option>
                    <option value="popular">最受欢迎</option>
                    <option value="rating">评分最高</option>
                </select>
            </div>
        </div>
    </section>

    <!-- MCP 服务列表 -->
    <main class="main-content">
        <div class="container">
            <div class="demo-notice">
                <h3>🚀 MCP 服务发布平台演示</h3>
                <p>这是基于 Vue 3 + Vite + TypeScript 构建的 MCP 服务发布平台的演示页面</p>
            </div>

            <div class="mcp-grid">
                <!-- Weather MCP -->
                <div class="mcp-card">
                    <div class="mcp-header">
                        <div class="mcp-icon">W</div>
                        <div class="mcp-info">
                            <h3 class="mcp-name">Weather MCP</h3>
                            <p class="mcp-author">by WeatherCorp</p>
                        </div>
                    </div>
                    
                    <p class="mcp-description">提供全球天气信息查询服务，支持实时天气、天气预报等功能</p>
                    
                    <div class="mcp-tags">
                        <span class="tag">天气</span>
                        <span class="tag">API</span>
                        <span class="tag">实用工具</span>
                    </div>
                    
                    <div class="mcp-stats">
                        <span>⬇ 1,250</span>
                        <span>⭐ 4.8</span>
                        <span>📅 2024-01-20</span>
                    </div>
                </div>

                <!-- Database Query MCP -->
                <div class="mcp-card">
                    <div class="mcp-header">
                        <div class="mcp-icon">DB</div>
                        <div class="mcp-info">
                            <h3 class="mcp-name">Database Query MCP</h3>
                            <p class="mcp-author">by DataTools</p>
                        </div>
                    </div>
                    
                    <p class="mcp-description">数据库查询和管理工具，支持多种数据库类型</p>
                    
                    <div class="mcp-tags">
                        <span class="tag">数据库</span>
                        <span class="tag">查询</span>
                        <span class="tag">管理</span>
                    </div>
                    
                    <div class="mcp-stats">
                        <span>⬇ 890</span>
                        <span>⭐ 4.6</span>
                        <span>📅 2024-01-18</span>
                    </div>
                </div>

                <!-- AI Text Processor -->
                <div class="mcp-card">
                    <div class="mcp-header">
                        <div class="mcp-icon">AI</div>
                        <div class="mcp-info">
                            <h3 class="mcp-name">AI Text Processor</h3>
                            <p class="mcp-author">by AILab</p>
                        </div>
                    </div>
                    
                    <p class="mcp-description">AI 驱动的文本处理工具，支持文本分析、摘要生成等</p>
                    
                    <div class="mcp-tags">
                        <span class="tag">AI</span>
                        <span class="tag">文本处理</span>
                        <span class="tag">自然语言</span>
                    </div>
                    
                    <div class="mcp-stats">
                        <span>⬇ 2,100</span>
                        <span>⭐ 4.9</span>
                        <span>📅 2024-01-22</span>
                    </div>
                </div>

                <!-- File Manager MCP -->
                <div class="mcp-card">
                    <div class="mcp-header">
                        <div class="mcp-icon">FM</div>
                        <div class="mcp-info">
                            <h3 class="mcp-name">File Manager MCP</h3>
                            <p class="mcp-author">by FileUtils</p>
                        </div>
                    </div>
                    
                    <p class="mcp-description">强大的文件管理工具，支持文件操作、批量处理等功能</p>
                    
                    <div class="mcp-tags">
                        <span class="tag">文件管理</span>
                        <span class="tag">批量处理</span>
                        <span class="tag">实用工具</span>
                    </div>
                    
                    <div class="mcp-stats">
                        <span>⬇ 756</span>
                        <span>⭐ 4.5</span>
                        <span>📅 2024-01-15</span>
                    </div>
                </div>

                <!-- API Gateway MCP -->
                <div class="mcp-card">
                    <div class="mcp-header">
                        <div class="mcp-icon">AG</div>
                        <div class="mcp-info">
                            <h3 class="mcp-name">API Gateway MCP</h3>
                            <p class="mcp-author">by APITeam</p>
                        </div>
                    </div>
                    
                    <p class="mcp-description">API 网关服务，提供统一的 API 管理和调用接口</p>
                    
                    <div class="mcp-tags">
                        <span class="tag">API</span>
                        <span class="tag">网关</span>
                        <span class="tag">集成</span>
                    </div>
                    
                    <div class="mcp-stats">
                        <span>⬇ 1,420</span>
                        <span>⭐ 4.7</span>
                        <span>📅 2024-01-19</span>
                    </div>
                </div>

                <!-- Code Generator MCP -->
                <div class="mcp-card">
                    <div class="mcp-header">
                        <div class="mcp-icon">CG</div>
                        <div class="mcp-info">
                            <h3 class="mcp-name">Code Generator MCP</h3>
                            <p class="mcp-author">by DevTools</p>
                        </div>
                    </div>
                    
                    <p class="mcp-description">智能代码生成工具，支持多种编程语言和框架</p>
                    
                    <div class="mcp-tags">
                        <span class="tag">代码生成</span>
                        <span class="tag">开发工具</span>
                        <span class="tag">AI</span>
                    </div>
                    
                    <div class="mcp-stats">
                        <span>⬇ 3,200</span>
                        <span>⭐ 4.9</span>
                        <span>📅 2024-01-21</span>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // 简单的交互功能
        document.addEventListener('DOMContentLoaded', function() {
            // 搜索功能
            const searchInput = document.querySelector('.search-input');
            const searchBtn = document.querySelector('.search-btn');
            
            searchBtn.addEventListener('click', function() {
                const query = searchInput.value.trim();
                if (query) {
                    alert(`搜索: ${query}`);
                }
            });

            // 卡片点击
            const cards = document.querySelectorAll('.mcp-card');
            cards.forEach(card => {
                card.addEventListener('click', function() {
                    const name = card.querySelector('.mcp-name').textContent;
                    alert(`查看 ${name} 详情`);
                });
            });

            // 登录按钮
            const loginBtn = document.querySelector('.btn-primary');
            loginBtn.addEventListener('click', function(e) {
                e.preventDefault();
                alert('跳转到登录页面');
            });
        });
    </script>
</body>
</html>
