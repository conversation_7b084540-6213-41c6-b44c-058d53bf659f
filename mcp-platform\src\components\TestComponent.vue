<template>
  <div class="test-component">
    <h1>Vue 应用测试</h1>
    <p>当前时间: {{ currentTime }}</p>
    <p>当前路由: {{ $route.path }}</p>
    
    <div class="navigation">
      <h2>导航测试</h2>
      <button @click="$router.push('/')" class="nav-btn">首页</button>
      <button @click="$router.push('/login')" class="nav-btn">登录</button>
      <button @click="$router.push('/publish')" class="nav-btn">发布</button>
      <button @click="$router.push('/mcp/1')" class="nav-btn">详情</button>
    </div>
    
    <div class="counter">
      <h2>计数器测试</h2>
      <p>计数: {{ count }}</p>
      <button @click="increment" class="counter-btn">+1</button>
      <button @click="decrement" class="counter-btn">-1</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

const currentTime = ref('')
const count = ref(0)
let timer: number | null = null

const updateTime = () => {
  currentTime.value = new Date().toLocaleString()
}

const increment = () => {
  count.value++
}

const decrement = () => {
  count.value--
}

onMounted(() => {
  updateTime()
  timer = setInterval(updateTime, 1000)
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
})
</script>

<style scoped>
.test-component {
  padding: 2rem;
  max-width: 600px;
  margin: 0 auto;
  font-family: Arial, sans-serif;
}

.test-component h1 {
  color: #333;
  margin-bottom: 1rem;
}

.test-component h2 {
  color: #666;
  margin: 2rem 0 1rem 0;
  font-size: 1.2rem;
}

.test-component p {
  margin-bottom: 0.5rem;
  color: #555;
}

.navigation {
  margin: 2rem 0;
  padding: 1rem;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.nav-btn {
  margin: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.nav-btn:hover {
  background-color: #0056b3;
}

.counter {
  margin: 2rem 0;
  padding: 1rem;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.counter-btn {
  margin: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1.2rem;
}

.counter-btn:hover {
  background-color: #1e7e34;
}
</style>
