import { createRouter, createWebHistory } from 'vue-router'
import MCPMarketplace from '../views/MCPMarketplace.vue'
import MCPPublish from '../views/MCPPublish.vue'
import MCPDetail from '../views/MCPDetail.vue'
import Login from '../views/Login.vue'
import TestComponent from '../components/TestComponent.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'marketplace',
      component: MCPMarketplace,
    },
    {
      path: '/publish',
      name: 'publish',
      component: MCPPublish,
      meta: { requiresAuth: true }
    },
    {
      path: '/mcp/:id',
      name: 'detail',
      component: MCPDetail,
    },
    {
      path: '/login',
      name: 'login',
      component: Login,
    },
    {
      path: '/test',
      name: 'test',
      component: TestComponent,
    },
  ],
})

// 路由守卫 - 检查登录状态
router.beforeEach((to, from, next) => {
  const isLoggedIn = localStorage.getItem('user_token')

  if (to.meta.requiresAuth && !isLoggedIn) {
    next('/login')
  } else {
    next()
  }
})

export default router
