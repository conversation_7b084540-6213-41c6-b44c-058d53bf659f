<template>
  <div class="login-page">
    <div class="login-container">
      <div class="login-card">
        <div class="login-header">
          <h1 class="logo" @click="$router.push('/')">MCP 广场</h1>
          <h2>登录账户</h2>
          <p>登录后即可发布和管理您的 MCP 服务</p>
        </div>

        <form @submit.prevent="handleLogin" class="login-form">
          <div class="form-group">
            <label for="email">邮箱地址</label>
            <input 
              id="email"
              v-model="form.email" 
              type="email" 
              required 
              placeholder="输入您的邮箱地址"
              class="form-input"
              :disabled="loading"
            />
          </div>

          <div class="form-group">
            <label for="password">密码</label>
            <input 
              id="password"
              v-model="form.password" 
              type="password" 
              required 
              placeholder="输入您的密码"
              class="form-input"
              :disabled="loading"
            />
          </div>

          <div class="form-options">
            <label class="checkbox-label">
              <input 
                v-model="form.rememberMe" 
                type="checkbox"
                :disabled="loading"
              />
              记住我
            </label>
            <a href="#" class="forgot-link">忘记密码？</a>
          </div>

          <button 
            type="submit" 
            class="login-btn"
            :disabled="loading"
          >
            {{ loading ? '登录中...' : '登录' }}
          </button>

          <div class="divider">
            <span>或</span>
          </div>

          <div class="social-login">
            <button 
              type="button" 
              @click="loginWithGitHub"
              class="social-btn github-btn"
              :disabled="loading"
            >
              <i class="icon-github"></i>
              使用 GitHub 登录
            </button>
            
            <button 
              type="button" 
              @click="loginWithGoogle"
              class="social-btn google-btn"
              :disabled="loading"
            >
              <i class="icon-google"></i>
              使用 Google 登录
            </button>
          </div>

          <div class="signup-link">
            <p>还没有账户？ <a href="#" @click="showRegister = true">立即注册</a></p>
          </div>
        </form>
      </div>
    </div>

    <!-- 注册模态框 -->
    <div v-if="showRegister" class="modal-overlay" @click="showRegister = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>注册账户</h3>
          <button @click="showRegister = false" class="close-btn">×</button>
        </div>
        
        <form @submit.prevent="handleRegister" class="register-form">
          <div class="form-group">
            <label for="reg-name">用户名</label>
            <input 
              id="reg-name"
              v-model="registerForm.name" 
              type="text" 
              required 
              placeholder="输入用户名"
              class="form-input"
            />
          </div>

          <div class="form-group">
            <label for="reg-email">邮箱地址</label>
            <input 
              id="reg-email"
              v-model="registerForm.email" 
              type="email" 
              required 
              placeholder="输入邮箱地址"
              class="form-input"
            />
          </div>

          <div class="form-group">
            <label for="reg-password">密码</label>
            <input 
              id="reg-password"
              v-model="registerForm.password" 
              type="password" 
              required 
              placeholder="输入密码（至少6位）"
              class="form-input"
              minlength="6"
            />
          </div>

          <div class="form-group">
            <label for="reg-confirm">确认密码</label>
            <input 
              id="reg-confirm"
              v-model="registerForm.confirmPassword" 
              type="password" 
              required 
              placeholder="再次输入密码"
              class="form-input"
            />
          </div>

          <div class="form-group">
            <label class="checkbox-label">
              <input 
                v-model="registerForm.agreeTerms" 
                type="checkbox"
                required
              />
              我同意 <a href="#" target="_blank">服务条款</a> 和 <a href="#" target="_blank">隐私政策</a>
            </label>
          </div>

          <button 
            type="submit" 
            class="register-btn"
            :disabled="registerLoading"
          >
            {{ registerLoading ? '注册中...' : '注册' }}
          </button>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const registerLoading = ref(false)
const showRegister = ref(false)

const form = reactive({
  email: '',
  password: '',
  rememberMe: false
})

const registerForm = reactive({
  name: '',
  email: '',
  password: '',
  confirmPassword: '',
  agreeTerms: false
})

// 方法
const handleLogin = async () => {
  loading.value = true
  
  try {
    // 模拟 API 调用
    console.log('登录信息:', form)
    
    // 模拟登录过程
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // 模拟登录成功
    const mockToken = 'mock_jwt_token_' + Date.now()
    const mockUser = {
      id: '1',
      name: 'Test User',
      email: form.email,
      avatar: '/api/placeholder/48/48'
    }
    
    localStorage.setItem('user_token', mockToken)
    localStorage.setItem('user_info', JSON.stringify(mockUser))
    
    // 跳转到首页或之前的页面
    const redirect = router.currentRoute.value.query.redirect as string
    router.push(redirect || '/')
    
  } catch (error) {
    console.error('登录失败:', error)
    alert('登录失败，请检查邮箱和密码')
  } finally {
    loading.value = false
  }
}

const handleRegister = async () => {
  if (registerForm.password !== registerForm.confirmPassword) {
    alert('两次输入的密码不一致')
    return
  }
  
  registerLoading.value = true
  
  try {
    // 模拟 API 调用
    console.log('注册信息:', registerForm)
    
    // 模拟注册过程
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    alert('注册成功！请登录您的账户')
    showRegister.value = false
    
    // 自动填充登录表单
    form.email = registerForm.email
    
  } catch (error) {
    console.error('注册失败:', error)
    alert('注册失败，请重试')
  } finally {
    registerLoading.value = false
  }
}

const loginWithGitHub = async () => {
  loading.value = true
  
  try {
    // 模拟 GitHub OAuth 登录
    console.log('GitHub 登录')
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟登录成功
    const mockToken = 'github_token_' + Date.now()
    const mockUser = {
      id: '2',
      name: 'GitHub User',
      email: '<EMAIL>',
      avatar: '/api/placeholder/48/48'
    }
    
    localStorage.setItem('user_token', mockToken)
    localStorage.setItem('user_info', JSON.stringify(mockUser))
    
    router.push('/')
    
  } catch (error) {
    console.error('GitHub 登录失败:', error)
    alert('GitHub 登录失败，请重试')
  } finally {
    loading.value = false
  }
}

const loginWithGoogle = async () => {
  loading.value = true
  
  try {
    // 模拟 Google OAuth 登录
    console.log('Google 登录')
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟登录成功
    const mockToken = 'google_token_' + Date.now()
    const mockUser = {
      id: '3',
      name: 'Google User',
      email: '<EMAIL>',
      avatar: '/api/placeholder/48/48'
    }
    
    localStorage.setItem('user_token', mockToken)
    localStorage.setItem('user_info', JSON.stringify(mockUser))
    
    router.push('/')
    
  } catch (error) {
    console.error('Google 登录失败:', error)
    alert('Google 登录失败，请重试')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.login-container {
  width: 100%;
  max-width: 400px;
}

.login-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.logo {
  font-size: 1.8rem;
  font-weight: bold;
  color: #333;
  margin: 0 0 1rem 0;
  cursor: pointer;
}

.login-header h2 {
  font-size: 1.5rem;
  color: #333;
  margin: 0 0 0.5rem 0;
}

.login-header p {
  color: #666;
  margin: 0;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

.form-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s;
}

.form-input:focus {
  outline: none;
  border-color: #007bff;
}

.form-input:disabled {
  background-color: #f8f9fa;
  cursor: not-allowed;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #666;
  cursor: pointer;
}

.checkbox-label input {
  width: auto;
}

.forgot-link {
  color: #007bff;
  text-decoration: none;
  font-size: 0.9rem;
}

.forgot-link:hover {
  text-decoration: underline;
}

.login-btn,
.register-btn {
  width: 100%;
  padding: 0.75rem;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.login-btn:hover:not(:disabled),
.register-btn:hover:not(:disabled) {
  background-color: #0056b3;
}

.login-btn:disabled,
.register-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.divider {
  text-align: center;
  margin: 1.5rem 0;
  position: relative;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background-color: #e0e0e0;
}

.divider span {
  background: white;
  padding: 0 1rem;
  color: #666;
  font-size: 0.9rem;
}

.social-login {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.social-btn {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: white;
  color: #333;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.social-btn:hover:not(:disabled) {
  background-color: #f8f9fa;
}

.social-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.github-btn:hover:not(:disabled) {
  background-color: #333;
  color: white;
}

.google-btn:hover:not(:disabled) {
  background-color: #db4437;
  color: white;
}

.signup-link {
  text-align: center;
  margin: 0;
}

.signup-link p {
  color: #666;
  margin: 0;
}

.signup-link a {
  color: #007bff;
  text-decoration: none;
}

.signup-link a:hover {
  text-decoration: underline;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.modal-content {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  width: 100%;
  max-width: 400px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: #333;
}

.register-form .form-group:last-of-type {
  margin-bottom: 1.5rem;
}

@media (max-width: 480px) {
  .login-page {
    padding: 1rem;
  }
  
  .login-card {
    padding: 1.5rem;
  }
}
</style>
