(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))s(o);new MutationObserver(o=>{for(const r of o)if(r.type==="childList")for(const i of r.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&s(i)}).observe(document,{childList:!0,subtree:!0});function n(o){const r={};return o.integrity&&(r.integrity=o.integrity),o.referrerPolicy&&(r.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?r.credentials="include":o.crossOrigin==="anonymous"?r.credentials="omit":r.credentials="same-origin",r}function s(o){if(o.ep)return;o.ep=!0;const r=n(o);fetch(o.href,r)}})();/**
* @vue/shared v3.5.17
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Cs(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const ee={},It=[],We=()=>{},pi=()=>!1,Ln=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),xs=e=>e.startsWith("onUpdate:"),me=Object.assign,Ss=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},hi=Object.prototype.hasOwnProperty,J=(e,t)=>hi.call(e,t),H=Array.isArray,kt=e=>pn(e)==="[object Map]",jt=e=>pn(e)==="[object Set]",Gs=e=>pn(e)==="[object Date]",U=e=>typeof e=="function",re=e=>typeof e=="string",qe=e=>typeof e=="symbol",se=e=>e!==null&&typeof e=="object",Ko=e=>(se(e)||U(e))&&U(e.then)&&U(e.catch),Wo=Object.prototype.toString,pn=e=>Wo.call(e),gi=e=>pn(e).slice(8,-1),qo=e=>pn(e)==="[object Object]",Es=e=>re(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Jt=Cs(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Nn=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},mi=/-(\w)/g,ht=Nn(e=>e.replace(mi,(t,n)=>n?n.toUpperCase():"")),vi=/\B([A-Z])/g,Et=Nn(e=>e.replace(vi,"-$1").toLowerCase()),Go=Nn(e=>e.charAt(0).toUpperCase()+e.slice(1)),Jn=Nn(e=>e?`on${Go(e)}`:""),pt=(e,t)=>!Object.is(e,t),wn=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},ls=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},An=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let zs;const Fn=()=>zs||(zs=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Ps(e){if(H(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],o=re(s)?wi(s):Ps(s);if(o)for(const r in o)t[r]=o[r]}return t}else if(re(e)||se(e))return e}const _i=/;(?![^(]*\))/g,bi=/:([^]+)/,yi=/\/\*[^]*?\*\//g;function wi(e){const t={};return e.replace(yi,"").split(_i).forEach(n=>{if(n){const s=n.split(bi);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function Rs(e){let t="";if(re(e))t=e;else if(H(e))for(let n=0;n<e.length;n++){const s=Rs(e[n]);s&&(t+=s+" ")}else if(se(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const Ci="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",xi=Cs(Ci);function zo(e){return!!e||e===""}function Si(e,t){if(e.length!==t.length)return!1;let n=!0;for(let s=0;n&&s<e.length;s++)n=hn(e[s],t[s]);return n}function hn(e,t){if(e===t)return!0;let n=Gs(e),s=Gs(t);if(n||s)return n&&s?e.getTime()===t.getTime():!1;if(n=qe(e),s=qe(t),n||s)return e===t;if(n=H(e),s=H(t),n||s)return n&&s?Si(e,t):!1;if(n=se(e),s=se(t),n||s){if(!n||!s)return!1;const o=Object.keys(e).length,r=Object.keys(t).length;if(o!==r)return!1;for(const i in e){const l=e.hasOwnProperty(i),c=t.hasOwnProperty(i);if(l&&!c||!l&&c||!hn(e[i],t[i]))return!1}}return String(e)===String(t)}function As(e,t){return e.findIndex(n=>hn(n,t))}const Jo=e=>!!(e&&e.__v_isRef===!0),W=e=>re(e)?e:e==null?"":H(e)||se(e)&&(e.toString===Wo||!U(e.toString))?Jo(e)?W(e.value):JSON.stringify(e,Qo,2):String(e),Qo=(e,t)=>Jo(t)?Qo(e,t.value):kt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,o],r)=>(n[Qn(s,r)+" =>"]=o,n),{})}:jt(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Qn(n))}:qe(t)?Qn(t):se(t)&&!H(t)&&!qo(t)?String(t):t,Qn=(e,t="")=>{var n;return qe(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let xe;class Yo{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=xe,!t&&xe&&(this.index=(xe.scopes||(xe.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=xe;try{return xe=this,t()}finally{xe=n}}}on(){++this._on===1&&(this.prevScope=xe,xe=this)}off(){this._on>0&&--this._on===0&&(xe=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const o=this.parent.scopes.pop();o&&o!==this&&(this.parent.scopes[this.index]=o,o.index=this.index)}this.parent=void 0}}}function Ei(e){return new Yo(e)}function Pi(){return xe}let ne;const Yn=new WeakSet;class Xo{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,xe&&xe.active&&xe.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Yn.has(this)&&(Yn.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||er(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Js(this),tr(this);const t=ne,n=Me;ne=this,Me=!0;try{return this.fn()}finally{nr(this),ne=t,Me=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Os(t);this.deps=this.depsTail=void 0,Js(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Yn.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){cs(this)&&this.run()}get dirty(){return cs(this)}}let Zo=0,Qt,Yt;function er(e,t=!1){if(e.flags|=8,t){e.next=Yt,Yt=e;return}e.next=Qt,Qt=e}function $s(){Zo++}function Ts(){if(--Zo>0)return;if(Yt){let t=Yt;for(Yt=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;Qt;){let t=Qt;for(Qt=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function tr(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function nr(e){let t,n=e.depsTail,s=n;for(;s;){const o=s.prevDep;s.version===-1?(s===n&&(n=o),Os(s),Ri(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=o}e.deps=t,e.depsTail=n}function cs(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(sr(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function sr(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===on)||(e.globalVersion=on,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!cs(e))))return;e.flags|=2;const t=e.dep,n=ne,s=Me;ne=e,Me=!0;try{tr(e);const o=e.fn(e._value);(t.version===0||pt(o,e._value))&&(e.flags|=128,e._value=o,t.version++)}catch(o){throw t.version++,o}finally{ne=n,Me=s,nr(e),e.flags&=-3}}function Os(e,t=!1){const{dep:n,prevSub:s,nextSub:o}=e;if(s&&(s.nextSub=o,e.prevSub=void 0),o&&(o.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let r=n.computed.deps;r;r=r.nextDep)Os(r,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function Ri(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let Me=!0;const or=[];function nt(){or.push(Me),Me=!1}function st(){const e=or.pop();Me=e===void 0?!0:e}function Js(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=ne;ne=void 0;try{t()}finally{ne=n}}}let on=0;class Ai{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Ms{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(t){if(!ne||!Me||ne===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==ne)n=this.activeLink=new Ai(ne,this),ne.deps?(n.prevDep=ne.depsTail,ne.depsTail.nextDep=n,ne.depsTail=n):ne.deps=ne.depsTail=n,rr(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=ne.depsTail,n.nextDep=void 0,ne.depsTail.nextDep=n,ne.depsTail=n,ne.deps===n&&(ne.deps=s)}return n}trigger(t){this.version++,on++,this.notify(t)}notify(t){$s();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{Ts()}}}function rr(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)rr(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const as=new WeakMap,Ct=Symbol(""),us=Symbol(""),rn=Symbol("");function fe(e,t,n){if(Me&&ne){let s=as.get(e);s||as.set(e,s=new Map);let o=s.get(n);o||(s.set(n,o=new Ms),o.map=s,o.key=n),o.track()}}function Ze(e,t,n,s,o,r){const i=as.get(e);if(!i){on++;return}const l=c=>{c&&c.trigger()};if($s(),t==="clear")i.forEach(l);else{const c=H(e),m=c&&Es(n);if(c&&n==="length"){const p=Number(s);i.forEach((g,f)=>{(f==="length"||f===rn||!qe(f)&&f>=p)&&l(g)})}else switch((n!==void 0||i.has(void 0))&&l(i.get(n)),m&&l(i.get(rn)),t){case"add":c?m&&l(i.get("length")):(l(i.get(Ct)),kt(e)&&l(i.get(us)));break;case"delete":c||(l(i.get(Ct)),kt(e)&&l(i.get(us)));break;case"set":kt(e)&&l(i.get(Ct));break}}Ts()}function Tt(e){const t=z(e);return t===e?t:(fe(t,"iterate",rn),Oe(e)?t:t.map(ue))}function Hn(e){return fe(e=z(e),"iterate",rn),e}const $i={__proto__:null,[Symbol.iterator](){return Xn(this,Symbol.iterator,ue)},concat(...e){return Tt(this).concat(...e.map(t=>H(t)?Tt(t):t))},entries(){return Xn(this,"entries",e=>(e[1]=ue(e[1]),e))},every(e,t){return Je(this,"every",e,t,void 0,arguments)},filter(e,t){return Je(this,"filter",e,t,n=>n.map(ue),arguments)},find(e,t){return Je(this,"find",e,t,ue,arguments)},findIndex(e,t){return Je(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Je(this,"findLast",e,t,ue,arguments)},findLastIndex(e,t){return Je(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Je(this,"forEach",e,t,void 0,arguments)},includes(...e){return Zn(this,"includes",e)},indexOf(...e){return Zn(this,"indexOf",e)},join(e){return Tt(this).join(e)},lastIndexOf(...e){return Zn(this,"lastIndexOf",e)},map(e,t){return Je(this,"map",e,t,void 0,arguments)},pop(){return Wt(this,"pop")},push(...e){return Wt(this,"push",e)},reduce(e,...t){return Qs(this,"reduce",e,t)},reduceRight(e,...t){return Qs(this,"reduceRight",e,t)},shift(){return Wt(this,"shift")},some(e,t){return Je(this,"some",e,t,void 0,arguments)},splice(...e){return Wt(this,"splice",e)},toReversed(){return Tt(this).toReversed()},toSorted(e){return Tt(this).toSorted(e)},toSpliced(...e){return Tt(this).toSpliced(...e)},unshift(...e){return Wt(this,"unshift",e)},values(){return Xn(this,"values",ue)}};function Xn(e,t,n){const s=Hn(e),o=s[t]();return s!==e&&!Oe(e)&&(o._next=o.next,o.next=()=>{const r=o._next();return r.value&&(r.value=n(r.value)),r}),o}const Ti=Array.prototype;function Je(e,t,n,s,o,r){const i=Hn(e),l=i!==e&&!Oe(e),c=i[t];if(c!==Ti[t]){const g=c.apply(e,r);return l?ue(g):g}let m=n;i!==e&&(l?m=function(g,f){return n.call(this,ue(g),f,e)}:n.length>2&&(m=function(g,f){return n.call(this,g,f,e)}));const p=c.call(i,m,s);return l&&o?o(p):p}function Qs(e,t,n,s){const o=Hn(e);let r=n;return o!==e&&(Oe(e)?n.length>3&&(r=function(i,l,c){return n.call(this,i,l,c,e)}):r=function(i,l,c){return n.call(this,i,ue(l),c,e)}),o[t](r,...s)}function Zn(e,t,n){const s=z(e);fe(s,"iterate",rn);const o=s[t](...n);return(o===-1||o===!1)&&Ds(n[0])?(n[0]=z(n[0]),s[t](...n)):o}function Wt(e,t,n=[]){nt(),$s();const s=z(e)[t].apply(e,n);return Ts(),st(),s}const Oi=Cs("__proto__,__v_isRef,__isVue"),ir=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(qe));function Mi(e){qe(e)||(e=String(e));const t=z(this);return fe(t,"has",e),t.hasOwnProperty(e)}class lr{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const o=this._isReadonly,r=this._isShallow;if(n==="__v_isReactive")return!o;if(n==="__v_isReadonly")return o;if(n==="__v_isShallow")return r;if(n==="__v_raw")return s===(o?r?ji:fr:r?ur:ar).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const i=H(t);if(!o){let c;if(i&&(c=$i[n]))return c;if(n==="hasOwnProperty")return Mi}const l=Reflect.get(t,n,ge(t)?t:s);return(qe(n)?ir.has(n):Oi(n))||(o||fe(t,"get",n),r)?l:ge(l)?i&&Es(n)?l:l.value:se(l)?o?pr(l):St(l):l}}class cr extends lr{constructor(t=!1){super(!1,t)}set(t,n,s,o){let r=t[n];if(!this._isShallow){const c=gt(r);if(!Oe(s)&&!gt(s)&&(r=z(r),s=z(s)),!H(t)&&ge(r)&&!ge(s))return c?!1:(r.value=s,!0)}const i=H(t)&&Es(n)?Number(n)<t.length:J(t,n),l=Reflect.set(t,n,s,ge(t)?t:o);return t===z(o)&&(i?pt(s,r)&&Ze(t,"set",n,s):Ze(t,"add",n,s)),l}deleteProperty(t,n){const s=J(t,n);t[n];const o=Reflect.deleteProperty(t,n);return o&&s&&Ze(t,"delete",n,void 0),o}has(t,n){const s=Reflect.has(t,n);return(!qe(n)||!ir.has(n))&&fe(t,"has",n),s}ownKeys(t){return fe(t,"iterate",H(t)?"length":Ct),Reflect.ownKeys(t)}}class Ii extends lr{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const ki=new cr,Di=new Ii,Li=new cr(!0);const fs=e=>e,_n=e=>Reflect.getPrototypeOf(e);function Ni(e,t,n){return function(...s){const o=this.__v_raw,r=z(o),i=kt(r),l=e==="entries"||e===Symbol.iterator&&i,c=e==="keys"&&i,m=o[e](...s),p=n?fs:t?$n:ue;return!t&&fe(r,"iterate",c?us:Ct),{next(){const{value:g,done:f}=m.next();return f?{value:g,done:f}:{value:l?[p(g[0]),p(g[1])]:p(g),done:f}},[Symbol.iterator](){return this}}}}function bn(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Fi(e,t){const n={get(o){const r=this.__v_raw,i=z(r),l=z(o);e||(pt(o,l)&&fe(i,"get",o),fe(i,"get",l));const{has:c}=_n(i),m=t?fs:e?$n:ue;if(c.call(i,o))return m(r.get(o));if(c.call(i,l))return m(r.get(l));r!==i&&r.get(o)},get size(){const o=this.__v_raw;return!e&&fe(z(o),"iterate",Ct),Reflect.get(o,"size",o)},has(o){const r=this.__v_raw,i=z(r),l=z(o);return e||(pt(o,l)&&fe(i,"has",o),fe(i,"has",l)),o===l?r.has(o):r.has(o)||r.has(l)},forEach(o,r){const i=this,l=i.__v_raw,c=z(l),m=t?fs:e?$n:ue;return!e&&fe(c,"iterate",Ct),l.forEach((p,g)=>o.call(r,m(p),m(g),i))}};return me(n,e?{add:bn("add"),set:bn("set"),delete:bn("delete"),clear:bn("clear")}:{add(o){!t&&!Oe(o)&&!gt(o)&&(o=z(o));const r=z(this);return _n(r).has.call(r,o)||(r.add(o),Ze(r,"add",o,o)),this},set(o,r){!t&&!Oe(r)&&!gt(r)&&(r=z(r));const i=z(this),{has:l,get:c}=_n(i);let m=l.call(i,o);m||(o=z(o),m=l.call(i,o));const p=c.call(i,o);return i.set(o,r),m?pt(r,p)&&Ze(i,"set",o,r):Ze(i,"add",o,r),this},delete(o){const r=z(this),{has:i,get:l}=_n(r);let c=i.call(r,o);c||(o=z(o),c=i.call(r,o)),l&&l.call(r,o);const m=r.delete(o);return c&&Ze(r,"delete",o,void 0),m},clear(){const o=z(this),r=o.size!==0,i=o.clear();return r&&Ze(o,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(o=>{n[o]=Ni(o,e,t)}),n}function Is(e,t){const n=Fi(e,t);return(s,o,r)=>o==="__v_isReactive"?!e:o==="__v_isReadonly"?e:o==="__v_raw"?s:Reflect.get(J(n,o)&&o in s?n:s,o,r)}const Hi={get:Is(!1,!1)},Vi={get:Is(!1,!0)},Ui={get:Is(!0,!1)};const ar=new WeakMap,ur=new WeakMap,fr=new WeakMap,ji=new WeakMap;function Bi(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Ki(e){return e.__v_skip||!Object.isExtensible(e)?0:Bi(gi(e))}function St(e){return gt(e)?e:ks(e,!1,ki,Hi,ar)}function dr(e){return ks(e,!1,Li,Vi,ur)}function pr(e){return ks(e,!0,Di,Ui,fr)}function ks(e,t,n,s,o){if(!se(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const r=Ki(e);if(r===0)return e;const i=o.get(e);if(i)return i;const l=new Proxy(e,r===2?s:n);return o.set(e,l),l}function Dt(e){return gt(e)?Dt(e.__v_raw):!!(e&&e.__v_isReactive)}function gt(e){return!!(e&&e.__v_isReadonly)}function Oe(e){return!!(e&&e.__v_isShallow)}function Ds(e){return e?!!e.__v_raw:!1}function z(e){const t=e&&e.__v_raw;return t?z(t):e}function hr(e){return!J(e,"__v_skip")&&Object.isExtensible(e)&&ls(e,"__v_skip",!0),e}const ue=e=>se(e)?St(e):e,$n=e=>se(e)?pr(e):e;function ge(e){return e?e.__v_isRef===!0:!1}function de(e){return gr(e,!1)}function Wi(e){return gr(e,!0)}function gr(e,t){return ge(e)?e:new qi(e,t)}class qi{constructor(t,n){this.dep=new Ms,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:z(t),this._value=n?t:ue(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||Oe(t)||gt(t);t=s?t:z(t),pt(t,n)&&(this._rawValue=t,this._value=s?t:ue(t),this.dep.trigger())}}function xt(e){return ge(e)?e.value:e}const Gi={get:(e,t,n)=>t==="__v_raw"?e:xt(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const o=e[t];return ge(o)&&!ge(n)?(o.value=n,!0):Reflect.set(e,t,n,s)}};function mr(e){return Dt(e)?e:new Proxy(e,Gi)}class zi{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new Ms(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=on-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&ne!==this)return er(this,!0),!0}get value(){const t=this.dep.track();return sr(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function Ji(e,t,n=!1){let s,o;return U(e)?s=e:(s=e.get,o=e.set),new zi(s,o,n)}const yn={},Tn=new WeakMap;let yt;function Qi(e,t=!1,n=yt){if(n){let s=Tn.get(n);s||Tn.set(n,s=[]),s.push(e)}}function Yi(e,t,n=ee){const{immediate:s,deep:o,once:r,scheduler:i,augmentJob:l,call:c}=n,m=k=>o?k:Oe(k)||o===!1||o===0?et(k,1):et(k);let p,g,f,a,_=!1,S=!1;if(ge(e)?(g=()=>e.value,_=Oe(e)):Dt(e)?(g=()=>m(e),_=!0):H(e)?(S=!0,_=e.some(k=>Dt(k)||Oe(k)),g=()=>e.map(k=>{if(ge(k))return k.value;if(Dt(k))return m(k);if(U(k))return c?c(k,2):k()})):U(e)?t?g=c?()=>c(e,2):e:g=()=>{if(f){nt();try{f()}finally{st()}}const k=yt;yt=p;try{return c?c(e,3,[a]):e(a)}finally{yt=k}}:g=We,t&&o){const k=g,X=o===!0?1/0:o;g=()=>et(k(),X)}const I=Pi(),E=()=>{p.stop(),I&&I.active&&Ss(I.effects,p)};if(r&&t){const k=t;t=(...X)=>{k(...X),E()}}let P=S?new Array(e.length).fill(yn):yn;const L=k=>{if(!(!(p.flags&1)||!p.dirty&&!k))if(t){const X=p.run();if(o||_||(S?X.some((ae,oe)=>pt(ae,P[oe])):pt(X,P))){f&&f();const ae=yt;yt=p;try{const oe=[X,P===yn?void 0:S&&P[0]===yn?[]:P,a];P=X,c?c(t,3,oe):t(...oe)}finally{yt=ae}}}else p.run()};return l&&l(L),p=new Xo(g),p.scheduler=i?()=>i(L,!1):L,a=k=>Qi(k,!1,p),f=p.onStop=()=>{const k=Tn.get(p);if(k){if(c)c(k,4);else for(const X of k)X();Tn.delete(p)}},t?s?L(!0):P=p.run():i?i(L.bind(null,!0),!0):p.run(),E.pause=p.pause.bind(p),E.resume=p.resume.bind(p),E.stop=E,E}function et(e,t=1/0,n){if(t<=0||!se(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,ge(e))et(e.value,t,n);else if(H(e))for(let s=0;s<e.length;s++)et(e[s],t,n);else if(jt(e)||kt(e))e.forEach(s=>{et(s,t,n)});else if(qo(e)){for(const s in e)et(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&et(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function gn(e,t,n,s){try{return s?e(...s):e()}catch(o){Vn(o,t,n)}}function Ge(e,t,n,s){if(U(e)){const o=gn(e,t,n,s);return o&&Ko(o)&&o.catch(r=>{Vn(r,t,n)}),o}if(H(e)){const o=[];for(let r=0;r<e.length;r++)o.push(Ge(e[r],t,n,s));return o}}function Vn(e,t,n,s=!0){const o=t?t.vnode:null,{errorHandler:r,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||ee;if(t){let l=t.parent;const c=t.proxy,m=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const p=l.ec;if(p){for(let g=0;g<p.length;g++)if(p[g](e,c,m)===!1)return}l=l.parent}if(r){nt(),gn(r,null,10,[e,c,m]),st();return}}Xi(e,n,o,s,i)}function Xi(e,t,n,s=!0,o=!1){if(o)throw e;console.error(e)}const _e=[];let Be=-1;const Lt=[];let at=null,Ot=0;const vr=Promise.resolve();let On=null;function Ls(e){const t=On||vr;return e?t.then(this?e.bind(this):e):t}function Zi(e){let t=Be+1,n=_e.length;for(;t<n;){const s=t+n>>>1,o=_e[s],r=ln(o);r<e||r===e&&o.flags&2?t=s+1:n=s}return t}function Ns(e){if(!(e.flags&1)){const t=ln(e),n=_e[_e.length-1];!n||!(e.flags&2)&&t>=ln(n)?_e.push(e):_e.splice(Zi(t),0,e),e.flags|=1,_r()}}function _r(){On||(On=vr.then(yr))}function el(e){H(e)?Lt.push(...e):at&&e.id===-1?at.splice(Ot+1,0,e):e.flags&1||(Lt.push(e),e.flags|=1),_r()}function Ys(e,t,n=Be+1){for(;n<_e.length;n++){const s=_e[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;_e.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function br(e){if(Lt.length){const t=[...new Set(Lt)].sort((n,s)=>ln(n)-ln(s));if(Lt.length=0,at){at.push(...t);return}for(at=t,Ot=0;Ot<at.length;Ot++){const n=at[Ot];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}at=null,Ot=0}}const ln=e=>e.id==null?e.flags&2?-1:1/0:e.id;function yr(e){try{for(Be=0;Be<_e.length;Be++){const t=_e[Be];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),gn(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Be<_e.length;Be++){const t=_e[Be];t&&(t.flags&=-2)}Be=-1,_e.length=0,br(),On=null,(_e.length||Lt.length)&&yr()}}let Te=null,wr=null;function Mn(e){const t=Te;return Te=e,wr=e&&e.type.__scopeId||null,t}function tl(e,t=Te,n){if(!t||e._n)return e;const s=(...o)=>{s._d&&io(-1);const r=Mn(t);let i;try{i=e(...o)}finally{Mn(r),s._d&&io(1)}return i};return s._n=!0,s._c=!0,s._d=!0,s}function ie(e,t){if(Te===null)return e;const n=Kn(Te),s=e.dirs||(e.dirs=[]);for(let o=0;o<t.length;o++){let[r,i,l,c=ee]=t[o];r&&(U(r)&&(r={mounted:r,updated:r}),r.deep&&et(i),s.push({dir:r,instance:n,value:i,oldValue:void 0,arg:l,modifiers:c}))}return e}function _t(e,t,n,s){const o=e.dirs,r=t&&t.dirs;for(let i=0;i<o.length;i++){const l=o[i];r&&(l.oldValue=r[i].value);let c=l.dir[s];c&&(nt(),Ge(c,n,8,[e.el,l,e,t]),st())}}const nl=Symbol("_vte"),sl=e=>e.__isTeleport;function Fs(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Fs(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function Pt(e,t){return U(e)?me({name:e.name},t,{setup:e}):e}function Cr(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Xt(e,t,n,s,o=!1){if(H(e)){e.forEach((_,S)=>Xt(_,t&&(H(t)?t[S]:t),n,s,o));return}if(Zt(s)&&!o){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&Xt(e,t,n,s.component.subTree);return}const r=s.shapeFlag&4?Kn(s.component):s.el,i=o?null:r,{i:l,r:c}=e,m=t&&t.r,p=l.refs===ee?l.refs={}:l.refs,g=l.setupState,f=z(g),a=g===ee?()=>!1:_=>J(f,_);if(m!=null&&m!==c&&(re(m)?(p[m]=null,a(m)&&(g[m]=null)):ge(m)&&(m.value=null)),U(c))gn(c,l,12,[i,p]);else{const _=re(c),S=ge(c);if(_||S){const I=()=>{if(e.f){const E=_?a(c)?g[c]:p[c]:c.value;o?H(E)&&Ss(E,r):H(E)?E.includes(r)||E.push(r):_?(p[c]=[r],a(c)&&(g[c]=p[c])):(c.value=[r],e.k&&(p[e.k]=c.value))}else _?(p[c]=i,a(c)&&(g[c]=i)):S&&(c.value=i,e.k&&(p[e.k]=i))};i?(I.id=-1,Re(I,n)):I()}}}Fn().requestIdleCallback;Fn().cancelIdleCallback;const Zt=e=>!!e.type.__asyncLoader,xr=e=>e.type.__isKeepAlive;function ol(e,t){Sr(e,"a",t)}function rl(e,t){Sr(e,"da",t)}function Sr(e,t,n=be){const s=e.__wdc||(e.__wdc=()=>{let o=n;for(;o;){if(o.isDeactivated)return;o=o.parent}return e()});if(Un(t,s,n),n){let o=n.parent;for(;o&&o.parent;)xr(o.parent.vnode)&&il(s,t,n,o),o=o.parent}}function il(e,t,n,s){const o=Un(t,e,s,!0);Er(()=>{Ss(s[t],o)},n)}function Un(e,t,n=be,s=!1){if(n){const o=n[e]||(n[e]=[]),r=t.__weh||(t.__weh=(...i)=>{nt();const l=mn(n),c=Ge(t,n,e,i);return l(),st(),c});return s?o.unshift(r):o.push(r),r}}const ot=e=>(t,n=be)=>{(!an||e==="sp")&&Un(e,(...s)=>t(...s),n)},ll=ot("bm"),Hs=ot("m"),cl=ot("bu"),al=ot("u"),ul=ot("bum"),Er=ot("um"),fl=ot("sp"),dl=ot("rtg"),pl=ot("rtc");function hl(e,t=be){Un("ec",e,t)}const gl=Symbol.for("v-ndc");function wt(e,t,n,s){let o;const r=n,i=H(e);if(i||re(e)){const l=i&&Dt(e);let c=!1,m=!1;l&&(c=!Oe(e),m=gt(e),e=Hn(e)),o=new Array(e.length);for(let p=0,g=e.length;p<g;p++)o[p]=t(c?m?$n(ue(e[p])):ue(e[p]):e[p],p,void 0,r)}else if(typeof e=="number"){o=new Array(e);for(let l=0;l<e;l++)o[l]=t(l+1,l,void 0,r)}else if(se(e))if(e[Symbol.iterator])o=Array.from(e,(l,c)=>t(l,c,void 0,r));else{const l=Object.keys(e);o=new Array(l.length);for(let c=0,m=l.length;c<m;c++){const p=l[c];o[c]=t(e[p],p,c,r)}}else o=[];return o}const ds=e=>e?Gr(e)?Kn(e):ds(e.parent):null,en=me(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>ds(e.parent),$root:e=>ds(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Rr(e),$forceUpdate:e=>e.f||(e.f=()=>{Ns(e.update)}),$nextTick:e=>e.n||(e.n=Ls.bind(e.proxy)),$watch:e=>Ll.bind(e)}),es=(e,t)=>e!==ee&&!e.__isScriptSetup&&J(e,t),ml={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:o,props:r,accessCache:i,type:l,appContext:c}=e;let m;if(t[0]!=="$"){const a=i[t];if(a!==void 0)switch(a){case 1:return s[t];case 2:return o[t];case 4:return n[t];case 3:return r[t]}else{if(es(s,t))return i[t]=1,s[t];if(o!==ee&&J(o,t))return i[t]=2,o[t];if((m=e.propsOptions[0])&&J(m,t))return i[t]=3,r[t];if(n!==ee&&J(n,t))return i[t]=4,n[t];ps&&(i[t]=0)}}const p=en[t];let g,f;if(p)return t==="$attrs"&&fe(e.attrs,"get",""),p(e);if((g=l.__cssModules)&&(g=g[t]))return g;if(n!==ee&&J(n,t))return i[t]=4,n[t];if(f=c.config.globalProperties,J(f,t))return f[t]},set({_:e},t,n){const{data:s,setupState:o,ctx:r}=e;return es(o,t)?(o[t]=n,!0):s!==ee&&J(s,t)?(s[t]=n,!0):J(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(r[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:o,propsOptions:r}},i){let l;return!!n[i]||e!==ee&&J(e,i)||es(t,i)||(l=r[0])&&J(l,i)||J(s,i)||J(en,i)||J(o.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:J(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Xs(e){return H(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let ps=!0;function vl(e){const t=Rr(e),n=e.proxy,s=e.ctx;ps=!1,t.beforeCreate&&Zs(t.beforeCreate,e,"bc");const{data:o,computed:r,methods:i,watch:l,provide:c,inject:m,created:p,beforeMount:g,mounted:f,beforeUpdate:a,updated:_,activated:S,deactivated:I,beforeDestroy:E,beforeUnmount:P,destroyed:L,unmounted:k,render:X,renderTracked:ae,renderTriggered:oe,errorCaptured:De,serverPrefetch:rt,expose:Le,inheritAttrs:it,components:vt,directives:Ne,filters:Bt}=t;if(m&&_l(m,s,null),i)for(const Y in i){const q=i[Y];U(q)&&(s[Y]=q.bind(n))}if(o){const Y=o.call(n,n);se(Y)&&(e.data=St(Y))}if(ps=!0,r)for(const Y in r){const q=r[Y],ze=U(q)?q.bind(n,n):U(q.get)?q.get.bind(n,n):We,lt=!U(q)&&U(q.set)?q.set.bind(n):We,Fe=he({get:ze,set:lt});Object.defineProperty(s,Y,{enumerable:!0,configurable:!0,get:()=>Fe.value,set:ye=>Fe.value=ye})}if(l)for(const Y in l)Pr(l[Y],s,n,Y);if(c){const Y=U(c)?c.call(n):c;Reflect.ownKeys(Y).forEach(q=>{Cn(q,Y[q])})}p&&Zs(p,e,"c");function ce(Y,q){H(q)?q.forEach(ze=>Y(ze.bind(n))):q&&Y(q.bind(n))}if(ce(ll,g),ce(Hs,f),ce(cl,a),ce(al,_),ce(ol,S),ce(rl,I),ce(hl,De),ce(pl,ae),ce(dl,oe),ce(ul,P),ce(Er,k),ce(fl,rt),H(Le))if(Le.length){const Y=e.exposed||(e.exposed={});Le.forEach(q=>{Object.defineProperty(Y,q,{get:()=>n[q],set:ze=>n[q]=ze})})}else e.exposed||(e.exposed={});X&&e.render===We&&(e.render=X),it!=null&&(e.inheritAttrs=it),vt&&(e.components=vt),Ne&&(e.directives=Ne),rt&&Cr(e)}function _l(e,t,n=We){H(e)&&(e=hs(e));for(const s in e){const o=e[s];let r;se(o)?"default"in o?r=Ie(o.from||s,o.default,!0):r=Ie(o.from||s):r=Ie(o),ge(r)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>r.value,set:i=>r.value=i}):t[s]=r}}function Zs(e,t,n){Ge(H(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function Pr(e,t,n,s){let o=s.includes(".")?Vr(n,s):()=>n[s];if(re(e)){const r=t[e];U(r)&&xn(o,r)}else if(U(e))xn(o,e.bind(n));else if(se(e))if(H(e))e.forEach(r=>Pr(r,t,n,s));else{const r=U(e.handler)?e.handler.bind(n):t[e.handler];U(r)&&xn(o,r,e)}}function Rr(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:o,optionsCache:r,config:{optionMergeStrategies:i}}=e.appContext,l=r.get(t);let c;return l?c=l:!o.length&&!n&&!s?c=t:(c={},o.length&&o.forEach(m=>In(c,m,i,!0)),In(c,t,i)),se(t)&&r.set(t,c),c}function In(e,t,n,s=!1){const{mixins:o,extends:r}=t;r&&In(e,r,n,!0),o&&o.forEach(i=>In(e,i,n,!0));for(const i in t)if(!(s&&i==="expose")){const l=bl[i]||n&&n[i];e[i]=l?l(e[i],t[i]):t[i]}return e}const bl={data:eo,props:to,emits:to,methods:zt,computed:zt,beforeCreate:ve,created:ve,beforeMount:ve,mounted:ve,beforeUpdate:ve,updated:ve,beforeDestroy:ve,beforeUnmount:ve,destroyed:ve,unmounted:ve,activated:ve,deactivated:ve,errorCaptured:ve,serverPrefetch:ve,components:zt,directives:zt,watch:wl,provide:eo,inject:yl};function eo(e,t){return t?e?function(){return me(U(e)?e.call(this,this):e,U(t)?t.call(this,this):t)}:t:e}function yl(e,t){return zt(hs(e),hs(t))}function hs(e){if(H(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function ve(e,t){return e?[...new Set([].concat(e,t))]:t}function zt(e,t){return e?me(Object.create(null),e,t):t}function to(e,t){return e?H(e)&&H(t)?[...new Set([...e,...t])]:me(Object.create(null),Xs(e),Xs(t??{})):t}function wl(e,t){if(!e)return t;if(!t)return e;const n=me(Object.create(null),e);for(const s in t)n[s]=ve(e[s],t[s]);return n}function Ar(){return{app:null,config:{isNativeTag:pi,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Cl=0;function xl(e,t){return function(s,o=null){U(s)||(s=me({},s)),o!=null&&!se(o)&&(o=null);const r=Ar(),i=new WeakSet,l=[];let c=!1;const m=r.app={_uid:Cl++,_component:s,_props:o,_container:null,_context:r,_instance:null,version:nc,get config(){return r.config},set config(p){},use(p,...g){return i.has(p)||(p&&U(p.install)?(i.add(p),p.install(m,...g)):U(p)&&(i.add(p),p(m,...g))),m},mixin(p){return r.mixins.includes(p)||r.mixins.push(p),m},component(p,g){return g?(r.components[p]=g,m):r.components[p]},directive(p,g){return g?(r.directives[p]=g,m):r.directives[p]},mount(p,g,f){if(!c){const a=m._ceVNode||Ee(s,o);return a.appContext=r,f===!0?f="svg":f===!1&&(f=void 0),e(a,p,f),c=!0,m._container=p,p.__vue_app__=m,Kn(a.component)}},onUnmount(p){l.push(p)},unmount(){c&&(Ge(l,m._instance,16),e(null,m._container),delete m._container.__vue_app__)},provide(p,g){return r.provides[p]=g,m},runWithContext(p){const g=Nt;Nt=m;try{return p()}finally{Nt=g}}};return m}}let Nt=null;function Cn(e,t){if(be){let n=be.provides;const s=be.parent&&be.parent.provides;s===n&&(n=be.provides=Object.create(s)),n[e]=t}}function Ie(e,t,n=!1){const s=be||Te;if(s||Nt){let o=Nt?Nt._context.provides:s?s.parent==null||s.ce?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(o&&e in o)return o[e];if(arguments.length>1)return n&&U(t)?t.call(s&&s.proxy):t}}const $r={},Tr=()=>Object.create($r),Or=e=>Object.getPrototypeOf(e)===$r;function Sl(e,t,n,s=!1){const o={},r=Tr();e.propsDefaults=Object.create(null),Mr(e,t,o,r);for(const i in e.propsOptions[0])i in o||(o[i]=void 0);n?e.props=s?o:dr(o):e.type.props?e.props=o:e.props=r,e.attrs=r}function El(e,t,n,s){const{props:o,attrs:r,vnode:{patchFlag:i}}=e,l=z(o),[c]=e.propsOptions;let m=!1;if((s||i>0)&&!(i&16)){if(i&8){const p=e.vnode.dynamicProps;for(let g=0;g<p.length;g++){let f=p[g];if(jn(e.emitsOptions,f))continue;const a=t[f];if(c)if(J(r,f))a!==r[f]&&(r[f]=a,m=!0);else{const _=ht(f);o[_]=gs(c,l,_,a,e,!1)}else a!==r[f]&&(r[f]=a,m=!0)}}}else{Mr(e,t,o,r)&&(m=!0);let p;for(const g in l)(!t||!J(t,g)&&((p=Et(g))===g||!J(t,p)))&&(c?n&&(n[g]!==void 0||n[p]!==void 0)&&(o[g]=gs(c,l,g,void 0,e,!0)):delete o[g]);if(r!==l)for(const g in r)(!t||!J(t,g))&&(delete r[g],m=!0)}m&&Ze(e.attrs,"set","")}function Mr(e,t,n,s){const[o,r]=e.propsOptions;let i=!1,l;if(t)for(let c in t){if(Jt(c))continue;const m=t[c];let p;o&&J(o,p=ht(c))?!r||!r.includes(p)?n[p]=m:(l||(l={}))[p]=m:jn(e.emitsOptions,c)||(!(c in s)||m!==s[c])&&(s[c]=m,i=!0)}if(r){const c=z(n),m=l||ee;for(let p=0;p<r.length;p++){const g=r[p];n[g]=gs(o,c,g,m[g],e,!J(m,g))}}return i}function gs(e,t,n,s,o,r){const i=e[n];if(i!=null){const l=J(i,"default");if(l&&s===void 0){const c=i.default;if(i.type!==Function&&!i.skipFactory&&U(c)){const{propsDefaults:m}=o;if(n in m)s=m[n];else{const p=mn(o);s=m[n]=c.call(null,t),p()}}else s=c;o.ce&&o.ce._setProp(n,s)}i[0]&&(r&&!l?s=!1:i[1]&&(s===""||s===Et(n))&&(s=!0))}return s}const Pl=new WeakMap;function Ir(e,t,n=!1){const s=n?Pl:t.propsCache,o=s.get(e);if(o)return o;const r=e.props,i={},l=[];let c=!1;if(!U(e)){const p=g=>{c=!0;const[f,a]=Ir(g,t,!0);me(i,f),a&&l.push(...a)};!n&&t.mixins.length&&t.mixins.forEach(p),e.extends&&p(e.extends),e.mixins&&e.mixins.forEach(p)}if(!r&&!c)return se(e)&&s.set(e,It),It;if(H(r))for(let p=0;p<r.length;p++){const g=ht(r[p]);no(g)&&(i[g]=ee)}else if(r)for(const p in r){const g=ht(p);if(no(g)){const f=r[p],a=i[g]=H(f)||U(f)?{type:f}:me({},f),_=a.type;let S=!1,I=!0;if(H(_))for(let E=0;E<_.length;++E){const P=_[E],L=U(P)&&P.name;if(L==="Boolean"){S=!0;break}else L==="String"&&(I=!1)}else S=U(_)&&_.name==="Boolean";a[0]=S,a[1]=I,(S||J(a,"default"))&&l.push(g)}}const m=[i,l];return se(e)&&s.set(e,m),m}function no(e){return e[0]!=="$"&&!Jt(e)}const Vs=e=>e[0]==="_"||e==="$stable",Us=e=>H(e)?e.map(Ke):[Ke(e)],Rl=(e,t,n)=>{if(t._n)return t;const s=tl((...o)=>Us(t(...o)),n);return s._c=!1,s},kr=(e,t,n)=>{const s=e._ctx;for(const o in e){if(Vs(o))continue;const r=e[o];if(U(r))t[o]=Rl(o,r,s);else if(r!=null){const i=Us(r);t[o]=()=>i}}},Dr=(e,t)=>{const n=Us(t);e.slots.default=()=>n},Lr=(e,t,n)=>{for(const s in t)(n||!Vs(s))&&(e[s]=t[s])},Al=(e,t,n)=>{const s=e.slots=Tr();if(e.vnode.shapeFlag&32){const o=t.__;o&&ls(s,"__",o,!0);const r=t._;r?(Lr(s,t,n),n&&ls(s,"_",r,!0)):kr(t,s)}else t&&Dr(e,t)},$l=(e,t,n)=>{const{vnode:s,slots:o}=e;let r=!0,i=ee;if(s.shapeFlag&32){const l=t._;l?n&&l===1?r=!1:Lr(o,t,n):(r=!t.$stable,kr(t,o)),i=t}else t&&(Dr(e,t),i={default:1});if(r)for(const l in o)!Vs(l)&&i[l]==null&&delete o[l]},Re=Bl;function Tl(e){return Ol(e)}function Ol(e,t){const n=Fn();n.__VUE__=!0;const{insert:s,remove:o,patchProp:r,createElement:i,createText:l,createComment:c,setText:m,setElementText:p,parentNode:g,nextSibling:f,setScopeId:a=We,insertStaticContent:_}=e,S=(u,h,v,b=null,C=null,w=null,$=void 0,A=null,R=!!h.dynamicChildren)=>{if(u===h)return;u&&!qt(u,h)&&(b=y(u),ye(u,C,w,!0),u=null),h.patchFlag===-2&&(R=!1,h.dynamicChildren=null);const{type:x,ref:F,shapeFlag:O}=h;switch(x){case Bn:I(u,h,v,b);break;case mt:E(u,h,v,b);break;case Sn:u==null&&P(h,v,b,$);break;case pe:vt(u,h,v,b,C,w,$,A,R);break;default:O&1?X(u,h,v,b,C,w,$,A,R):O&6?Ne(u,h,v,b,C,w,$,A,R):(O&64||O&128)&&x.process(u,h,v,b,C,w,$,A,R,D)}F!=null&&C?Xt(F,u&&u.ref,w,h||u,!h):F==null&&u&&u.ref!=null&&Xt(u.ref,null,w,u,!0)},I=(u,h,v,b)=>{if(u==null)s(h.el=l(h.children),v,b);else{const C=h.el=u.el;h.children!==u.children&&m(C,h.children)}},E=(u,h,v,b)=>{u==null?s(h.el=c(h.children||""),v,b):h.el=u.el},P=(u,h,v,b)=>{[u.el,u.anchor]=_(u.children,h,v,b,u.el,u.anchor)},L=({el:u,anchor:h},v,b)=>{let C;for(;u&&u!==h;)C=f(u),s(u,v,b),u=C;s(h,v,b)},k=({el:u,anchor:h})=>{let v;for(;u&&u!==h;)v=f(u),o(u),u=v;o(h)},X=(u,h,v,b,C,w,$,A,R)=>{h.type==="svg"?$="svg":h.type==="math"&&($="mathml"),u==null?ae(h,v,b,C,w,$,A,R):rt(u,h,C,w,$,A,R)},ae=(u,h,v,b,C,w,$,A)=>{let R,x;const{props:F,shapeFlag:O,transition:N,dirs:V}=u;if(R=u.el=i(u.type,w,F&&F.is,F),O&8?p(R,u.children):O&16&&De(u.children,R,null,b,C,ts(u,w),$,A),V&&_t(u,null,b,"created"),oe(R,u,u.scopeId,$,b),F){for(const te in F)te!=="value"&&!Jt(te)&&r(R,te,null,F[te],w,b);"value"in F&&r(R,"value",null,F.value,w),(x=F.onVnodeBeforeMount)&&je(x,b,u)}V&&_t(u,null,b,"beforeMount");const B=Ml(C,N);B&&N.beforeEnter(R),s(R,h,v),((x=F&&F.onVnodeMounted)||B||V)&&Re(()=>{x&&je(x,b,u),B&&N.enter(R),V&&_t(u,null,b,"mounted")},C)},oe=(u,h,v,b,C)=>{if(v&&a(u,v),b)for(let w=0;w<b.length;w++)a(u,b[w]);if(C){let w=C.subTree;if(h===w||jr(w.type)&&(w.ssContent===h||w.ssFallback===h)){const $=C.vnode;oe(u,$,$.scopeId,$.slotScopeIds,C.parent)}}},De=(u,h,v,b,C,w,$,A,R=0)=>{for(let x=R;x<u.length;x++){const F=u[x]=A?ut(u[x]):Ke(u[x]);S(null,F,h,v,b,C,w,$,A)}},rt=(u,h,v,b,C,w,$)=>{const A=h.el=u.el;let{patchFlag:R,dynamicChildren:x,dirs:F}=h;R|=u.patchFlag&16;const O=u.props||ee,N=h.props||ee;let V;if(v&&bt(v,!1),(V=N.onVnodeBeforeUpdate)&&je(V,v,h,u),F&&_t(h,u,v,"beforeUpdate"),v&&bt(v,!0),(O.innerHTML&&N.innerHTML==null||O.textContent&&N.textContent==null)&&p(A,""),x?Le(u.dynamicChildren,x,A,v,b,ts(h,C),w):$||q(u,h,A,null,v,b,ts(h,C),w,!1),R>0){if(R&16)it(A,O,N,v,C);else if(R&2&&O.class!==N.class&&r(A,"class",null,N.class,C),R&4&&r(A,"style",O.style,N.style,C),R&8){const B=h.dynamicProps;for(let te=0;te<B.length;te++){const Q=B[te],we=O[Q],Ce=N[Q];(Ce!==we||Q==="value")&&r(A,Q,we,Ce,C,v)}}R&1&&u.children!==h.children&&p(A,h.children)}else!$&&x==null&&it(A,O,N,v,C);((V=N.onVnodeUpdated)||F)&&Re(()=>{V&&je(V,v,h,u),F&&_t(h,u,v,"updated")},b)},Le=(u,h,v,b,C,w,$)=>{for(let A=0;A<h.length;A++){const R=u[A],x=h[A],F=R.el&&(R.type===pe||!qt(R,x)||R.shapeFlag&198)?g(R.el):v;S(R,x,F,null,b,C,w,$,!0)}},it=(u,h,v,b,C)=>{if(h!==v){if(h!==ee)for(const w in h)!Jt(w)&&!(w in v)&&r(u,w,h[w],null,C,b);for(const w in v){if(Jt(w))continue;const $=v[w],A=h[w];$!==A&&w!=="value"&&r(u,w,A,$,C,b)}"value"in v&&r(u,"value",h.value,v.value,C)}},vt=(u,h,v,b,C,w,$,A,R)=>{const x=h.el=u?u.el:l(""),F=h.anchor=u?u.anchor:l("");let{patchFlag:O,dynamicChildren:N,slotScopeIds:V}=h;V&&(A=A?A.concat(V):V),u==null?(s(x,v,b),s(F,v,b),De(h.children||[],v,F,C,w,$,A,R)):O>0&&O&64&&N&&u.dynamicChildren?(Le(u.dynamicChildren,N,v,C,w,$,A),(h.key!=null||C&&h===C.subTree)&&Nr(u,h,!0)):q(u,h,v,F,C,w,$,A,R)},Ne=(u,h,v,b,C,w,$,A,R)=>{h.slotScopeIds=A,u==null?h.shapeFlag&512?C.ctx.activate(h,v,b,$,R):Bt(h,v,b,C,w,$,R):Rt(u,h,R)},Bt=(u,h,v,b,C,w,$)=>{const A=u.component=Ql(u,b,C);if(xr(u)&&(A.ctx.renderer=D),Yl(A,!1,$),A.asyncDep){if(C&&C.registerDep(A,ce,$),!u.el){const R=A.subTree=Ee(mt);E(null,R,h,v)}}else ce(A,u,h,v,C,w,$)},Rt=(u,h,v)=>{const b=h.component=u.component;if(Ul(u,h,v))if(b.asyncDep&&!b.asyncResolved){Y(b,h,v);return}else b.next=h,b.update();else h.el=u.el,b.vnode=h},ce=(u,h,v,b,C,w,$)=>{const A=()=>{if(u.isMounted){let{next:O,bu:N,u:V,parent:B,vnode:te}=u;{const Ve=Fr(u);if(Ve){O&&(O.el=te.el,Y(u,O,$)),Ve.asyncDep.then(()=>{u.isUnmounted||A()});return}}let Q=O,we;bt(u,!1),O?(O.el=te.el,Y(u,O,$)):O=te,N&&wn(N),(we=O.props&&O.props.onVnodeBeforeUpdate)&&je(we,B,O,te),bt(u,!0);const Ce=oo(u),He=u.subTree;u.subTree=Ce,S(He,Ce,g(He.el),y(He),u,C,w),O.el=Ce.el,Q===null&&jl(u,Ce.el),V&&Re(V,C),(we=O.props&&O.props.onVnodeUpdated)&&Re(()=>je(we,B,O,te),C)}else{let O;const{el:N,props:V}=h,{bm:B,m:te,parent:Q,root:we,type:Ce}=u,He=Zt(h);bt(u,!1),B&&wn(B),!He&&(O=V&&V.onVnodeBeforeMount)&&je(O,Q,h),bt(u,!0);{we.ce&&we.ce._def.shadowRoot!==!1&&we.ce._injectChildStyle(Ce);const Ve=u.subTree=oo(u);S(null,Ve,v,b,u,C,w),h.el=Ve.el}if(te&&Re(te,C),!He&&(O=V&&V.onVnodeMounted)){const Ve=h;Re(()=>je(O,Q,Ve),C)}(h.shapeFlag&256||Q&&Zt(Q.vnode)&&Q.vnode.shapeFlag&256)&&u.a&&Re(u.a,C),u.isMounted=!0,h=v=b=null}};u.scope.on();const R=u.effect=new Xo(A);u.scope.off();const x=u.update=R.run.bind(R),F=u.job=R.runIfDirty.bind(R);F.i=u,F.id=u.uid,R.scheduler=()=>Ns(F),bt(u,!0),x()},Y=(u,h,v)=>{h.component=u;const b=u.vnode.props;u.vnode=h,u.next=null,El(u,h.props,b,v),$l(u,h.children,v),nt(),Ys(u),st()},q=(u,h,v,b,C,w,$,A,R=!1)=>{const x=u&&u.children,F=u?u.shapeFlag:0,O=h.children,{patchFlag:N,shapeFlag:V}=h;if(N>0){if(N&128){lt(x,O,v,b,C,w,$,A,R);return}else if(N&256){ze(x,O,v,b,C,w,$,A,R);return}}V&8?(F&16&&$e(x,C,w),O!==x&&p(v,O)):F&16?V&16?lt(x,O,v,b,C,w,$,A,R):$e(x,C,w,!0):(F&8&&p(v,""),V&16&&De(O,v,b,C,w,$,A,R))},ze=(u,h,v,b,C,w,$,A,R)=>{u=u||It,h=h||It;const x=u.length,F=h.length,O=Math.min(x,F);let N;for(N=0;N<O;N++){const V=h[N]=R?ut(h[N]):Ke(h[N]);S(u[N],V,v,null,C,w,$,A,R)}x>F?$e(u,C,w,!0,!1,O):De(h,v,b,C,w,$,A,R,O)},lt=(u,h,v,b,C,w,$,A,R)=>{let x=0;const F=h.length;let O=u.length-1,N=F-1;for(;x<=O&&x<=N;){const V=u[x],B=h[x]=R?ut(h[x]):Ke(h[x]);if(qt(V,B))S(V,B,v,null,C,w,$,A,R);else break;x++}for(;x<=O&&x<=N;){const V=u[O],B=h[N]=R?ut(h[N]):Ke(h[N]);if(qt(V,B))S(V,B,v,null,C,w,$,A,R);else break;O--,N--}if(x>O){if(x<=N){const V=N+1,B=V<F?h[V].el:b;for(;x<=N;)S(null,h[x]=R?ut(h[x]):Ke(h[x]),v,B,C,w,$,A,R),x++}}else if(x>N)for(;x<=O;)ye(u[x],C,w,!0),x++;else{const V=x,B=x,te=new Map;for(x=B;x<=N;x++){const Pe=h[x]=R?ut(h[x]):Ke(h[x]);Pe.key!=null&&te.set(Pe.key,x)}let Q,we=0;const Ce=N-B+1;let He=!1,Ve=0;const Kt=new Array(Ce);for(x=0;x<Ce;x++)Kt[x]=0;for(x=V;x<=O;x++){const Pe=u[x];if(we>=Ce){ye(Pe,C,w,!0);continue}let Ue;if(Pe.key!=null)Ue=te.get(Pe.key);else for(Q=B;Q<=N;Q++)if(Kt[Q-B]===0&&qt(Pe,h[Q])){Ue=Q;break}Ue===void 0?ye(Pe,C,w,!0):(Kt[Ue-B]=x+1,Ue>=Ve?Ve=Ue:He=!0,S(Pe,h[Ue],v,null,C,w,$,A,R),we++)}const Ws=He?Il(Kt):It;for(Q=Ws.length-1,x=Ce-1;x>=0;x--){const Pe=B+x,Ue=h[Pe],qs=Pe+1<F?h[Pe+1].el:b;Kt[x]===0?S(null,Ue,v,qs,C,w,$,A,R):He&&(Q<0||x!==Ws[Q]?Fe(Ue,v,qs,2):Q--)}}},Fe=(u,h,v,b,C=null)=>{const{el:w,type:$,transition:A,children:R,shapeFlag:x}=u;if(x&6){Fe(u.component.subTree,h,v,b);return}if(x&128){u.suspense.move(h,v,b);return}if(x&64){$.move(u,h,v,D);return}if($===pe){s(w,h,v);for(let O=0;O<R.length;O++)Fe(R[O],h,v,b);s(u.anchor,h,v);return}if($===Sn){L(u,h,v);return}if(b!==2&&x&1&&A)if(b===0)A.beforeEnter(w),s(w,h,v),Re(()=>A.enter(w),C);else{const{leave:O,delayLeave:N,afterLeave:V}=A,B=()=>{u.ctx.isUnmounted?o(w):s(w,h,v)},te=()=>{O(w,()=>{B(),V&&V()})};N?N(w,B,te):te()}else s(w,h,v)},ye=(u,h,v,b=!1,C=!1)=>{const{type:w,props:$,ref:A,children:R,dynamicChildren:x,shapeFlag:F,patchFlag:O,dirs:N,cacheIndex:V}=u;if(O===-2&&(C=!1),A!=null&&(nt(),Xt(A,null,v,u,!0),st()),V!=null&&(h.renderCache[V]=void 0),F&256){h.ctx.deactivate(u);return}const B=F&1&&N,te=!Zt(u);let Q;if(te&&(Q=$&&$.onVnodeBeforeUnmount)&&je(Q,h,u),F&6)vn(u.component,v,b);else{if(F&128){u.suspense.unmount(v,b);return}B&&_t(u,null,h,"beforeUnmount"),F&64?u.type.remove(u,h,v,D,b):x&&!x.hasOnce&&(w!==pe||O>0&&O&64)?$e(x,h,v,!1,!0):(w===pe&&O&384||!C&&F&16)&&$e(R,h,v),b&&At(u)}(te&&(Q=$&&$.onVnodeUnmounted)||B)&&Re(()=>{Q&&je(Q,h,u),B&&_t(u,null,h,"unmounted")},v)},At=u=>{const{type:h,el:v,anchor:b,transition:C}=u;if(h===pe){$t(v,b);return}if(h===Sn){k(u);return}const w=()=>{o(v),C&&!C.persisted&&C.afterLeave&&C.afterLeave()};if(u.shapeFlag&1&&C&&!C.persisted){const{leave:$,delayLeave:A}=C,R=()=>$(v,w);A?A(u.el,w,R):R()}else w()},$t=(u,h)=>{let v;for(;u!==h;)v=f(u),o(u),u=v;o(h)},vn=(u,h,v)=>{const{bum:b,scope:C,job:w,subTree:$,um:A,m:R,a:x,parent:F,slots:{__:O}}=u;so(R),so(x),b&&wn(b),F&&H(O)&&O.forEach(N=>{F.renderCache[N]=void 0}),C.stop(),w&&(w.flags|=8,ye($,u,h,v)),A&&Re(A,h),Re(()=>{u.isUnmounted=!0},h),h&&h.pendingBranch&&!h.isUnmounted&&u.asyncDep&&!u.asyncResolved&&u.suspenseId===h.pendingId&&(h.deps--,h.deps===0&&h.resolve())},$e=(u,h,v,b=!1,C=!1,w=0)=>{for(let $=w;$<u.length;$++)ye(u[$],h,v,b,C)},y=u=>{if(u.shapeFlag&6)return y(u.component.subTree);if(u.shapeFlag&128)return u.suspense.next();const h=f(u.anchor||u.el),v=h&&h[nl];return v?f(v):h};let M=!1;const T=(u,h,v)=>{u==null?h._vnode&&ye(h._vnode,null,null,!0):S(h._vnode||null,u,h,null,null,null,v),h._vnode=u,M||(M=!0,Ys(),br(),M=!1)},D={p:S,um:ye,m:Fe,r:At,mt:Bt,mc:De,pc:q,pbc:Le,n:y,o:e};return{render:T,hydrate:void 0,createApp:xl(T)}}function ts({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function bt({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Ml(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Nr(e,t,n=!1){const s=e.children,o=t.children;if(H(s)&&H(o))for(let r=0;r<s.length;r++){const i=s[r];let l=o[r];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=o[r]=ut(o[r]),l.el=i.el),!n&&l.patchFlag!==-2&&Nr(i,l)),l.type===Bn&&(l.el=i.el),l.type===mt&&!l.el&&(l.el=i.el)}}function Il(e){const t=e.slice(),n=[0];let s,o,r,i,l;const c=e.length;for(s=0;s<c;s++){const m=e[s];if(m!==0){if(o=n[n.length-1],e[o]<m){t[s]=o,n.push(s);continue}for(r=0,i=n.length-1;r<i;)l=r+i>>1,e[n[l]]<m?r=l+1:i=l;m<e[n[r]]&&(r>0&&(t[s]=n[r-1]),n[r]=s)}}for(r=n.length,i=n[r-1];r-- >0;)n[r]=i,i=t[i];return n}function Fr(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Fr(t)}function so(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const kl=Symbol.for("v-scx"),Dl=()=>Ie(kl);function xn(e,t,n){return Hr(e,t,n)}function Hr(e,t,n=ee){const{immediate:s,deep:o,flush:r,once:i}=n,l=me({},n),c=t&&s||!t&&r!=="post";let m;if(an){if(r==="sync"){const a=Dl();m=a.__watcherHandles||(a.__watcherHandles=[])}else if(!c){const a=()=>{};return a.stop=We,a.resume=We,a.pause=We,a}}const p=be;l.call=(a,_,S)=>Ge(a,p,_,S);let g=!1;r==="post"?l.scheduler=a=>{Re(a,p&&p.suspense)}:r!=="sync"&&(g=!0,l.scheduler=(a,_)=>{_?a():Ns(a)}),l.augmentJob=a=>{t&&(a.flags|=4),g&&(a.flags|=2,p&&(a.id=p.uid,a.i=p))};const f=Yi(e,t,l);return an&&(m?m.push(f):c&&f()),f}function Ll(e,t,n){const s=this.proxy,o=re(e)?e.includes(".")?Vr(s,e):()=>s[e]:e.bind(s,s);let r;U(t)?r=t:(r=t.handler,n=t);const i=mn(this),l=Hr(o,r.bind(s),n);return i(),l}function Vr(e,t){const n=t.split(".");return()=>{let s=e;for(let o=0;o<n.length&&s;o++)s=s[n[o]];return s}}const Nl=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${ht(t)}Modifiers`]||e[`${Et(t)}Modifiers`];function Fl(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||ee;let o=n;const r=t.startsWith("update:"),i=r&&Nl(s,t.slice(7));i&&(i.trim&&(o=n.map(p=>re(p)?p.trim():p)),i.number&&(o=n.map(An)));let l,c=s[l=Jn(t)]||s[l=Jn(ht(t))];!c&&r&&(c=s[l=Jn(Et(t))]),c&&Ge(c,e,6,o);const m=s[l+"Once"];if(m){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,Ge(m,e,6,o)}}function Ur(e,t,n=!1){const s=t.emitsCache,o=s.get(e);if(o!==void 0)return o;const r=e.emits;let i={},l=!1;if(!U(e)){const c=m=>{const p=Ur(m,t,!0);p&&(l=!0,me(i,p))};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}return!r&&!l?(se(e)&&s.set(e,null),null):(H(r)?r.forEach(c=>i[c]=null):me(i,r),se(e)&&s.set(e,i),i)}function jn(e,t){return!e||!Ln(t)?!1:(t=t.slice(2).replace(/Once$/,""),J(e,t[0].toLowerCase()+t.slice(1))||J(e,Et(t))||J(e,t))}function oo(e){const{type:t,vnode:n,proxy:s,withProxy:o,propsOptions:[r],slots:i,attrs:l,emit:c,render:m,renderCache:p,props:g,data:f,setupState:a,ctx:_,inheritAttrs:S}=e,I=Mn(e);let E,P;try{if(n.shapeFlag&4){const k=o||s,X=k;E=Ke(m.call(X,k,p,g,a,f,_)),P=l}else{const k=t;E=Ke(k.length>1?k(g,{attrs:l,slots:i,emit:c}):k(g,null)),P=t.props?l:Hl(l)}}catch(k){tn.length=0,Vn(k,e,1),E=Ee(mt)}let L=E;if(P&&S!==!1){const k=Object.keys(P),{shapeFlag:X}=L;k.length&&X&7&&(r&&k.some(xs)&&(P=Vl(P,r)),L=Ft(L,P,!1,!0))}return n.dirs&&(L=Ft(L,null,!1,!0),L.dirs=L.dirs?L.dirs.concat(n.dirs):n.dirs),n.transition&&Fs(L,n.transition),E=L,Mn(I),E}const Hl=e=>{let t;for(const n in e)(n==="class"||n==="style"||Ln(n))&&((t||(t={}))[n]=e[n]);return t},Vl=(e,t)=>{const n={};for(const s in e)(!xs(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function Ul(e,t,n){const{props:s,children:o,component:r}=e,{props:i,children:l,patchFlag:c}=t,m=r.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&c>=0){if(c&1024)return!0;if(c&16)return s?ro(s,i,m):!!i;if(c&8){const p=t.dynamicProps;for(let g=0;g<p.length;g++){const f=p[g];if(i[f]!==s[f]&&!jn(m,f))return!0}}}else return(o||l)&&(!l||!l.$stable)?!0:s===i?!1:s?i?ro(s,i,m):!0:!!i;return!1}function ro(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let o=0;o<s.length;o++){const r=s[o];if(t[r]!==e[r]&&!jn(n,r))return!0}return!1}function jl({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const jr=e=>e.__isSuspense;function Bl(e,t){t&&t.pendingBranch?H(e)?t.effects.push(...e):t.effects.push(e):el(e)}const pe=Symbol.for("v-fgt"),Bn=Symbol.for("v-txt"),mt=Symbol.for("v-cmt"),Sn=Symbol.for("v-stc"),tn=[];let Ae=null;function j(e=!1){tn.push(Ae=e?null:[])}function Kl(){tn.pop(),Ae=tn[tn.length-1]||null}let cn=1;function io(e,t=!1){cn+=e,e<0&&Ae&&t&&(Ae.hasOnce=!0)}function Br(e){return e.dynamicChildren=cn>0?Ae||It:null,Kl(),cn>0&&Ae&&Ae.push(e),e}function K(e,t,n,s,o,r){return Br(d(e,t,n,s,o,r,!0))}function Kr(e,t,n,s,o){return Br(Ee(e,t,n,s,o,!0))}function kn(e){return e?e.__v_isVNode===!0:!1}function qt(e,t){return e.type===t.type&&e.key===t.key}const Wr=({key:e})=>e??null,En=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?re(e)||ge(e)||U(e)?{i:Te,r:e,k:t,f:!!n}:e:null);function d(e,t=null,n=null,s=0,o=null,r=e===pe?0:1,i=!1,l=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Wr(t),ref:t&&En(t),scopeId:wr,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:r,patchFlag:s,dynamicProps:o,dynamicChildren:null,appContext:null,ctx:Te};return l?(js(c,n),r&128&&e.normalize(c)):n&&(c.shapeFlag|=re(n)?8:16),cn>0&&!i&&Ae&&(c.patchFlag>0||r&6)&&c.patchFlag!==32&&Ae.push(c),c}const Ee=Wl;function Wl(e,t=null,n=null,s=0,o=null,r=!1){if((!e||e===gl)&&(e=mt),kn(e)){const l=Ft(e,t,!0);return n&&js(l,n),cn>0&&!r&&Ae&&(l.shapeFlag&6?Ae[Ae.indexOf(e)]=l:Ae.push(l)),l.patchFlag=-2,l}if(tc(e)&&(e=e.__vccOpts),t){t=ql(t);let{class:l,style:c}=t;l&&!re(l)&&(t.class=Rs(l)),se(c)&&(Ds(c)&&!H(c)&&(c=me({},c)),t.style=Ps(c))}const i=re(e)?1:jr(e)?128:sl(e)?64:se(e)?4:U(e)?2:0;return d(e,t,n,s,o,i,r,!0)}function ql(e){return e?Ds(e)||Or(e)?me({},e):e:null}function Ft(e,t,n=!1,s=!1){const{props:o,ref:r,patchFlag:i,children:l,transition:c}=e,m=t?Gl(o||{},t):o,p={__v_isVNode:!0,__v_skip:!0,type:e.type,props:m,key:m&&Wr(m),ref:t&&t.ref?n&&r?H(r)?r.concat(En(t)):[r,En(t)]:En(t):r,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==pe?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Ft(e.ssContent),ssFallback:e.ssFallback&&Ft(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&s&&Fs(p,c.clone(p)),p}function le(e=" ",t=0){return Ee(Bn,null,e,t)}function qr(e,t){const n=Ee(Sn,null,e);return n.staticCount=t,n}function Xe(e="",t=!1){return t?(j(),Kr(mt,null,e)):Ee(mt,null,e)}function Ke(e){return e==null||typeof e=="boolean"?Ee(mt):H(e)?Ee(pe,null,e.slice()):kn(e)?ut(e):Ee(Bn,null,String(e))}function ut(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Ft(e)}function js(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(H(t))n=16;else if(typeof t=="object")if(s&65){const o=t.default;o&&(o._c&&(o._d=!1),js(e,o()),o._c&&(o._d=!0));return}else{n=32;const o=t._;!o&&!Or(t)?t._ctx=Te:o===3&&Te&&(Te.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else U(t)?(t={default:t,_ctx:Te},n=32):(t=String(t),s&64?(n=16,t=[le(t)]):n=8);e.children=t,e.shapeFlag|=n}function Gl(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const o in s)if(o==="class")t.class!==s.class&&(t.class=Rs([t.class,s.class]));else if(o==="style")t.style=Ps([t.style,s.style]);else if(Ln(o)){const r=t[o],i=s[o];i&&r!==i&&!(H(r)&&r.includes(i))&&(t[o]=r?[].concat(r,i):i)}else o!==""&&(t[o]=s[o])}return t}function je(e,t,n,s=null){Ge(e,t,7,[n,s])}const zl=Ar();let Jl=0;function Ql(e,t,n){const s=e.type,o=(t?t.appContext:e.appContext)||zl,r={uid:Jl++,vnode:e,type:s,parent:t,appContext:o,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Yo(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(o.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Ir(s,o),emitsOptions:Ur(s,o),emit:null,emitted:null,propsDefaults:ee,inheritAttrs:s.inheritAttrs,ctx:ee,data:ee,props:ee,attrs:ee,slots:ee,refs:ee,setupState:ee,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return r.ctx={_:r},r.root=t?t.root:r,r.emit=Fl.bind(null,r),e.ce&&e.ce(r),r}let be=null,Dn,ms;{const e=Fn(),t=(n,s)=>{let o;return(o=e[n])||(o=e[n]=[]),o.push(s),r=>{o.length>1?o.forEach(i=>i(r)):o[0](r)}};Dn=t("__VUE_INSTANCE_SETTERS__",n=>be=n),ms=t("__VUE_SSR_SETTERS__",n=>an=n)}const mn=e=>{const t=be;return Dn(e),e.scope.on(),()=>{e.scope.off(),Dn(t)}},lo=()=>{be&&be.scope.off(),Dn(null)};function Gr(e){return e.vnode.shapeFlag&4}let an=!1;function Yl(e,t=!1,n=!1){t&&ms(t);const{props:s,children:o}=e.vnode,r=Gr(e);Sl(e,s,r,t),Al(e,o,n||t);const i=r?Xl(e,t):void 0;return t&&ms(!1),i}function Xl(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,ml);const{setup:s}=n;if(s){nt();const o=e.setupContext=s.length>1?ec(e):null,r=mn(e),i=gn(s,e,0,[e.props,o]),l=Ko(i);if(st(),r(),(l||e.sp)&&!Zt(e)&&Cr(e),l){if(i.then(lo,lo),t)return i.then(c=>{co(e,c)}).catch(c=>{Vn(c,e,0)});e.asyncDep=i}else co(e,i)}else zr(e)}function co(e,t,n){U(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:se(t)&&(e.setupState=mr(t)),zr(e)}function zr(e,t,n){const s=e.type;e.render||(e.render=s.render||We);{const o=mn(e);nt();try{vl(e)}finally{st(),o()}}}const Zl={get(e,t){return fe(e,"get",""),e[t]}};function ec(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,Zl),slots:e.slots,emit:e.emit,expose:t}}function Kn(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(mr(hr(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in en)return en[n](e)},has(t,n){return n in t||n in en}})):e.proxy}function tc(e){return U(e)&&"__vccOpts"in e}const he=(e,t)=>Ji(e,t,an);function Jr(e,t,n){const s=arguments.length;return s===2?se(t)&&!H(t)?kn(t)?Ee(e,null,[t]):Ee(e,t):Ee(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&kn(n)&&(n=[n]),Ee(e,t,n))}const nc="3.5.17";/**
* @vue/runtime-dom v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let vs;const ao=typeof window<"u"&&window.trustedTypes;if(ao)try{vs=ao.createPolicy("vue",{createHTML:e=>e})}catch{}const Qr=vs?e=>vs.createHTML(e):e=>e,sc="http://www.w3.org/2000/svg",oc="http://www.w3.org/1998/Math/MathML",Ye=typeof document<"u"?document:null,uo=Ye&&Ye.createElement("template"),rc={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const o=t==="svg"?Ye.createElementNS(sc,e):t==="mathml"?Ye.createElementNS(oc,e):n?Ye.createElement(e,{is:n}):Ye.createElement(e);return e==="select"&&s&&s.multiple!=null&&o.setAttribute("multiple",s.multiple),o},createText:e=>Ye.createTextNode(e),createComment:e=>Ye.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Ye.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,o,r){const i=n?n.previousSibling:t.lastChild;if(o&&(o===r||o.nextSibling))for(;t.insertBefore(o.cloneNode(!0),n),!(o===r||!(o=o.nextSibling)););else{uo.innerHTML=Qr(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const l=uo.content;if(s==="svg"||s==="mathml"){const c=l.firstChild;for(;c.firstChild;)l.appendChild(c.firstChild);l.removeChild(c)}t.insertBefore(l,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},ic=Symbol("_vtc");function lc(e,t,n){const s=e[ic];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const fo=Symbol("_vod"),cc=Symbol("_vsh"),ac=Symbol(""),uc=/(^|;)\s*display\s*:/;function fc(e,t,n){const s=e.style,o=re(n);let r=!1;if(n&&!o){if(t)if(re(t))for(const i of t.split(";")){const l=i.slice(0,i.indexOf(":")).trim();n[l]==null&&Pn(s,l,"")}else for(const i in t)n[i]==null&&Pn(s,i,"");for(const i in n)i==="display"&&(r=!0),Pn(s,i,n[i])}else if(o){if(t!==n){const i=s[ac];i&&(n+=";"+i),s.cssText=n,r=uc.test(n)}}else t&&e.removeAttribute("style");fo in e&&(e[fo]=r?s.display:"",e[cc]&&(s.display="none"))}const po=/\s*!important$/;function Pn(e,t,n){if(H(n))n.forEach(s=>Pn(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=dc(e,t);po.test(n)?e.setProperty(Et(s),n.replace(po,""),"important"):e[s]=n}}const ho=["Webkit","Moz","ms"],ns={};function dc(e,t){const n=ns[t];if(n)return n;let s=ht(t);if(s!=="filter"&&s in e)return ns[t]=s;s=Go(s);for(let o=0;o<ho.length;o++){const r=ho[o]+s;if(r in e)return ns[t]=r}return t}const go="http://www.w3.org/1999/xlink";function mo(e,t,n,s,o,r=xi(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(go,t.slice(6,t.length)):e.setAttributeNS(go,t,n):n==null||r&&!zo(n)?e.removeAttribute(t):e.setAttribute(t,r?"":qe(n)?String(n):n)}function vo(e,t,n,s,o){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?Qr(n):n);return}const r=e.tagName;if(t==="value"&&r!=="PROGRESS"&&!r.includes("-")){const l=r==="OPTION"?e.getAttribute("value")||"":e.value,c=n==null?e.type==="checkbox"?"on":"":String(n);(l!==c||!("_value"in e))&&(e.value=c),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=zo(n):n==null&&l==="string"?(n="",i=!0):l==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(o||t)}function dt(e,t,n,s){e.addEventListener(t,n,s)}function pc(e,t,n,s){e.removeEventListener(t,n,s)}const _o=Symbol("_vei");function hc(e,t,n,s,o=null){const r=e[_o]||(e[_o]={}),i=r[t];if(s&&i)i.value=s;else{const[l,c]=gc(t);if(s){const m=r[t]=_c(s,o);dt(e,l,m,c)}else i&&(pc(e,l,i,c),r[t]=void 0)}}const bo=/(?:Once|Passive|Capture)$/;function gc(e){let t;if(bo.test(e)){t={};let s;for(;s=e.match(bo);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Et(e.slice(2)),t]}let ss=0;const mc=Promise.resolve(),vc=()=>ss||(mc.then(()=>ss=0),ss=Date.now());function _c(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;Ge(bc(s,n.value),t,5,[s])};return n.value=e,n.attached=vc(),n}function bc(e,t){if(H(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>o=>!o._stopped&&s&&s(o))}else return t}const yo=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,yc=(e,t,n,s,o,r)=>{const i=o==="svg";t==="class"?lc(e,s,i):t==="style"?fc(e,n,s):Ln(t)?xs(t)||hc(e,t,n,s,r):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):wc(e,t,s,i))?(vo(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&mo(e,t,s,i,r,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!re(s))?vo(e,ht(t),s,r,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),mo(e,t,s,i))};function wc(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&yo(t)&&U(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const o=e.tagName;if(o==="IMG"||o==="VIDEO"||o==="CANVAS"||o==="SOURCE")return!1}return yo(t)&&re(n)?!1:t in e}const Ht=e=>{const t=e.props["onUpdate:modelValue"]||!1;return H(t)?n=>wn(t,n):t};function Cc(e){e.target.composing=!0}function wo(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const tt=Symbol("_assign"),Se={created(e,{modifiers:{lazy:t,trim:n,number:s}},o){e[tt]=Ht(o);const r=s||o.props&&o.props.type==="number";dt(e,t?"change":"input",i=>{if(i.target.composing)return;let l=e.value;n&&(l=l.trim()),r&&(l=An(l)),e[tt](l)}),n&&dt(e,"change",()=>{e.value=e.value.trim()}),t||(dt(e,"compositionstart",Cc),dt(e,"compositionend",wo),dt(e,"change",wo))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:s,trim:o,number:r}},i){if(e[tt]=Ht(i),e.composing)return;const l=(r||e.type==="number")&&!/^0\d/.test(e.value)?An(e.value):e.value,c=t??"";l!==c&&(document.activeElement===e&&e.type!=="range"&&(s&&t===n||o&&e.value.trim()===c)||(e.value=c))}},Co={deep:!0,created(e,t,n){e[tt]=Ht(n),dt(e,"change",()=>{const s=e._modelValue,o=un(e),r=e.checked,i=e[tt];if(H(s)){const l=As(s,o),c=l!==-1;if(r&&!c)i(s.concat(o));else if(!r&&c){const m=[...s];m.splice(l,1),i(m)}}else if(jt(s)){const l=new Set(s);r?l.add(o):l.delete(o),i(l)}else i(Yr(e,r))})},mounted:xo,beforeUpdate(e,t,n){e[tt]=Ht(n),xo(e,t,n)}};function xo(e,{value:t,oldValue:n},s){e._modelValue=t;let o;if(H(t))o=As(t,s.props.value)>-1;else if(jt(t))o=t.has(s.props.value);else{if(t===n)return;o=hn(t,Yr(e,!0))}e.checked!==o&&(e.checked=o)}const _s={deep:!0,created(e,{value:t,modifiers:{number:n}},s){const o=jt(t);dt(e,"change",()=>{const r=Array.prototype.filter.call(e.options,i=>i.selected).map(i=>n?An(un(i)):un(i));e[tt](e.multiple?o?new Set(r):r:r[0]),e._assigning=!0,Ls(()=>{e._assigning=!1})}),e[tt]=Ht(s)},mounted(e,{value:t}){So(e,t)},beforeUpdate(e,t,n){e[tt]=Ht(n)},updated(e,{value:t}){e._assigning||So(e,t)}};function So(e,t){const n=e.multiple,s=H(t);if(!(n&&!s&&!jt(t))){for(let o=0,r=e.options.length;o<r;o++){const i=e.options[o],l=un(i);if(n)if(s){const c=typeof l;c==="string"||c==="number"?i.selected=t.some(m=>String(m)===String(l)):i.selected=As(t,l)>-1}else i.selected=t.has(l);else if(hn(un(i),t)){e.selectedIndex!==o&&(e.selectedIndex=o);return}}!n&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function un(e){return"_value"in e?e._value:e.value}function Yr(e,t){const n=t?"_trueValue":"_falseValue";return n in e?e[n]:t}const xc=["ctrl","shift","alt","meta"],Sc={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>xc.some(n=>e[`${n}Key`]&&!t.includes(n))},Rn=(e,t)=>{const n=e._withMods||(e._withMods={}),s=t.join(".");return n[s]||(n[s]=(o,...r)=>{for(let i=0;i<t.length;i++){const l=Sc[t[i]];if(l&&l(o,t))return}return e(o,...r)})},Ec=me({patchProp:yc},rc);let Eo;function Pc(){return Eo||(Eo=Tl(Ec))}const Rc=(...e)=>{const t=Pc().createApp(...e),{mount:n}=t;return t.mount=s=>{const o=$c(s);if(!o)return;const r=t._component;!U(r)&&!r.render&&!r.template&&(r.template=o.innerHTML),o.nodeType===1&&(o.textContent="");const i=n(o,!1,Ac(o));return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),i},t};function Ac(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function $c(e){return re(e)?document.querySelector(e):e}/*!
 * pinia v3.0.3
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */const Tc=Symbol();var Po;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(Po||(Po={}));function Oc(){const e=Ei(!0),t=e.run(()=>de({}));let n=[],s=[];const o=hr({install(r){o._a=r,r.provide(Tc,o),r.config.globalProperties.$pinia=o,s.forEach(i=>n.push(i)),s=[]},use(r){return this._a?n.push(r):s.push(r),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return o}/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const Mt=typeof document<"u";function Xr(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Mc(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Xr(e.default)}const G=Object.assign;function os(e,t){const n={};for(const s in t){const o=t[s];n[s]=ke(o)?o.map(e):e(o)}return n}const nn=()=>{},ke=Array.isArray,Zr=/#/g,Ic=/&/g,kc=/\//g,Dc=/=/g,Lc=/\?/g,ei=/\+/g,Nc=/%5B/g,Fc=/%5D/g,ti=/%5E/g,Hc=/%60/g,ni=/%7B/g,Vc=/%7C/g,si=/%7D/g,Uc=/%20/g;function Bs(e){return encodeURI(""+e).replace(Vc,"|").replace(Nc,"[").replace(Fc,"]")}function jc(e){return Bs(e).replace(ni,"{").replace(si,"}").replace(ti,"^")}function bs(e){return Bs(e).replace(ei,"%2B").replace(Uc,"+").replace(Zr,"%23").replace(Ic,"%26").replace(Hc,"`").replace(ni,"{").replace(si,"}").replace(ti,"^")}function Bc(e){return bs(e).replace(Dc,"%3D")}function Kc(e){return Bs(e).replace(Zr,"%23").replace(Lc,"%3F")}function Wc(e){return e==null?"":Kc(e).replace(kc,"%2F")}function fn(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const qc=/\/$/,Gc=e=>e.replace(qc,"");function rs(e,t,n="/"){let s,o={},r="",i="";const l=t.indexOf("#");let c=t.indexOf("?");return l<c&&l>=0&&(c=-1),c>-1&&(s=t.slice(0,c),r=t.slice(c+1,l>-1?l:t.length),o=e(r)),l>-1&&(s=s||t.slice(0,l),i=t.slice(l,t.length)),s=Yc(s??t,n),{fullPath:s+(r&&"?")+r+i,path:s,query:o,hash:fn(i)}}function zc(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function Ro(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Jc(e,t,n){const s=t.matched.length-1,o=n.matched.length-1;return s>-1&&s===o&&Vt(t.matched[s],n.matched[o])&&oi(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function Vt(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function oi(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Qc(e[n],t[n]))return!1;return!0}function Qc(e,t){return ke(e)?Ao(e,t):ke(t)?Ao(t,e):e===t}function Ao(e,t){return ke(t)?e.length===t.length&&e.every((n,s)=>n===t[s]):e.length===1&&e[0]===t}function Yc(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),s=e.split("/"),o=s[s.length-1];(o===".."||o===".")&&s.push("");let r=n.length-1,i,l;for(i=0;i<s.length;i++)if(l=s[i],l!==".")if(l==="..")r>1&&r--;else break;return n.slice(0,r).join("/")+"/"+s.slice(i).join("/")}const ct={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var dn;(function(e){e.pop="pop",e.push="push"})(dn||(dn={}));var sn;(function(e){e.back="back",e.forward="forward",e.unknown=""})(sn||(sn={}));function Xc(e){if(!e)if(Mt){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Gc(e)}const Zc=/^[^#]+#/;function ea(e,t){return e.replace(Zc,"#")+t}function ta(e,t){const n=document.documentElement.getBoundingClientRect(),s=e.getBoundingClientRect();return{behavior:t.behavior,left:s.left-n.left-(t.left||0),top:s.top-n.top-(t.top||0)}}const Wn=()=>({left:window.scrollX,top:window.scrollY});function na(e){let t;if("el"in e){const n=e.el,s=typeof n=="string"&&n.startsWith("#"),o=typeof n=="string"?s?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!o)return;t=ta(o,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function $o(e,t){return(history.state?history.state.position-t:-1)+e}const ys=new Map;function sa(e,t){ys.set(e,t)}function oa(e){const t=ys.get(e);return ys.delete(e),t}let ra=()=>location.protocol+"//"+location.host;function ri(e,t){const{pathname:n,search:s,hash:o}=t,r=e.indexOf("#");if(r>-1){let l=o.includes(e.slice(r))?e.slice(r).length:1,c=o.slice(l);return c[0]!=="/"&&(c="/"+c),Ro(c,"")}return Ro(n,e)+s+o}function ia(e,t,n,s){let o=[],r=[],i=null;const l=({state:f})=>{const a=ri(e,location),_=n.value,S=t.value;let I=0;if(f){if(n.value=a,t.value=f,i&&i===_){i=null;return}I=S?f.position-S.position:0}else s(a);o.forEach(E=>{E(n.value,_,{delta:I,type:dn.pop,direction:I?I>0?sn.forward:sn.back:sn.unknown})})};function c(){i=n.value}function m(f){o.push(f);const a=()=>{const _=o.indexOf(f);_>-1&&o.splice(_,1)};return r.push(a),a}function p(){const{history:f}=window;f.state&&f.replaceState(G({},f.state,{scroll:Wn()}),"")}function g(){for(const f of r)f();r=[],window.removeEventListener("popstate",l),window.removeEventListener("beforeunload",p)}return window.addEventListener("popstate",l),window.addEventListener("beforeunload",p,{passive:!0}),{pauseListeners:c,listen:m,destroy:g}}function To(e,t,n,s=!1,o=!1){return{back:e,current:t,forward:n,replaced:s,position:window.history.length,scroll:o?Wn():null}}function la(e){const{history:t,location:n}=window,s={value:ri(e,n)},o={value:t.state};o.value||r(s.value,{back:null,current:s.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function r(c,m,p){const g=e.indexOf("#"),f=g>-1?(n.host&&document.querySelector("base")?e:e.slice(g))+c:ra()+e+c;try{t[p?"replaceState":"pushState"](m,"",f),o.value=m}catch(a){console.error(a),n[p?"replace":"assign"](f)}}function i(c,m){const p=G({},t.state,To(o.value.back,c,o.value.forward,!0),m,{position:o.value.position});r(c,p,!0),s.value=c}function l(c,m){const p=G({},o.value,t.state,{forward:c,scroll:Wn()});r(p.current,p,!0);const g=G({},To(s.value,c,null),{position:p.position+1},m);r(c,g,!1),s.value=c}return{location:s,state:o,push:l,replace:i}}function ca(e){e=Xc(e);const t=la(e),n=ia(e,t.state,t.location,t.replace);function s(r,i=!0){i||n.pauseListeners(),history.go(r)}const o=G({location:"",base:e,go:s,createHref:ea.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function aa(e){return typeof e=="string"||e&&typeof e=="object"}function ii(e){return typeof e=="string"||typeof e=="symbol"}const li=Symbol("");var Oo;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Oo||(Oo={}));function Ut(e,t){return G(new Error,{type:e,[li]:!0},t)}function Qe(e,t){return e instanceof Error&&li in e&&(t==null||!!(e.type&t))}const Mo="[^/]+?",ua={sensitive:!1,strict:!1,start:!0,end:!0},fa=/[.+*?^${}()[\]/\\]/g;function da(e,t){const n=G({},ua,t),s=[];let o=n.start?"^":"";const r=[];for(const m of e){const p=m.length?[]:[90];n.strict&&!m.length&&(o+="/");for(let g=0;g<m.length;g++){const f=m[g];let a=40+(n.sensitive?.25:0);if(f.type===0)g||(o+="/"),o+=f.value.replace(fa,"\\$&"),a+=40;else if(f.type===1){const{value:_,repeatable:S,optional:I,regexp:E}=f;r.push({name:_,repeatable:S,optional:I});const P=E||Mo;if(P!==Mo){a+=10;try{new RegExp(`(${P})`)}catch(k){throw new Error(`Invalid custom RegExp for param "${_}" (${P}): `+k.message)}}let L=S?`((?:${P})(?:/(?:${P}))*)`:`(${P})`;g||(L=I&&m.length<2?`(?:/${L})`:"/"+L),I&&(L+="?"),o+=L,a+=20,I&&(a+=-8),S&&(a+=-20),P===".*"&&(a+=-50)}p.push(a)}s.push(p)}if(n.strict&&n.end){const m=s.length-1;s[m][s[m].length-1]+=.7000000000000001}n.strict||(o+="/?"),n.end?o+="$":n.strict&&!o.endsWith("/")&&(o+="(?:/|$)");const i=new RegExp(o,n.sensitive?"":"i");function l(m){const p=m.match(i),g={};if(!p)return null;for(let f=1;f<p.length;f++){const a=p[f]||"",_=r[f-1];g[_.name]=a&&_.repeatable?a.split("/"):a}return g}function c(m){let p="",g=!1;for(const f of e){(!g||!p.endsWith("/"))&&(p+="/"),g=!1;for(const a of f)if(a.type===0)p+=a.value;else if(a.type===1){const{value:_,repeatable:S,optional:I}=a,E=_ in m?m[_]:"";if(ke(E)&&!S)throw new Error(`Provided param "${_}" is an array but it is not repeatable (* or + modifiers)`);const P=ke(E)?E.join("/"):E;if(!P)if(I)f.length<2&&(p.endsWith("/")?p=p.slice(0,-1):g=!0);else throw new Error(`Missing required param "${_}"`);p+=P}}return p||"/"}return{re:i,score:s,keys:r,parse:l,stringify:c}}function pa(e,t){let n=0;for(;n<e.length&&n<t.length;){const s=t[n]-e[n];if(s)return s;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function ci(e,t){let n=0;const s=e.score,o=t.score;for(;n<s.length&&n<o.length;){const r=pa(s[n],o[n]);if(r)return r;n++}if(Math.abs(o.length-s.length)===1){if(Io(s))return 1;if(Io(o))return-1}return o.length-s.length}function Io(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const ha={type:0,value:""},ga=/[a-zA-Z0-9_]/;function ma(e){if(!e)return[[]];if(e==="/")return[[ha]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(a){throw new Error(`ERR (${n})/"${m}": ${a}`)}let n=0,s=n;const o=[];let r;function i(){r&&o.push(r),r=[]}let l=0,c,m="",p="";function g(){m&&(n===0?r.push({type:0,value:m}):n===1||n===2||n===3?(r.length>1&&(c==="*"||c==="+")&&t(`A repeatable param (${m}) must be alone in its segment. eg: '/:ids+.`),r.push({type:1,value:m,regexp:p,repeatable:c==="*"||c==="+",optional:c==="*"||c==="?"})):t("Invalid state to consume buffer"),m="")}function f(){m+=c}for(;l<e.length;){if(c=e[l++],c==="\\"&&n!==2){s=n,n=4;continue}switch(n){case 0:c==="/"?(m&&g(),i()):c===":"?(g(),n=1):f();break;case 4:f(),n=s;break;case 1:c==="("?n=2:ga.test(c)?f():(g(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&l--);break;case 2:c===")"?p[p.length-1]=="\\"?p=p.slice(0,-1)+c:n=3:p+=c;break;case 3:g(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&l--,p="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${m}"`),g(),i(),o}function va(e,t,n){const s=da(ma(e.path),n),o=G(s,{record:e,parent:t,children:[],alias:[]});return t&&!o.record.aliasOf==!t.record.aliasOf&&t.children.push(o),o}function _a(e,t){const n=[],s=new Map;t=No({strict:!1,end:!0,sensitive:!1},t);function o(g){return s.get(g)}function r(g,f,a){const _=!a,S=Do(g);S.aliasOf=a&&a.record;const I=No(t,g),E=[S];if("alias"in g){const k=typeof g.alias=="string"?[g.alias]:g.alias;for(const X of k)E.push(Do(G({},S,{components:a?a.record.components:S.components,path:X,aliasOf:a?a.record:S})))}let P,L;for(const k of E){const{path:X}=k;if(f&&X[0]!=="/"){const ae=f.record.path,oe=ae[ae.length-1]==="/"?"":"/";k.path=f.record.path+(X&&oe+X)}if(P=va(k,f,I),a?a.alias.push(P):(L=L||P,L!==P&&L.alias.push(P),_&&g.name&&!Lo(P)&&i(g.name)),ai(P)&&c(P),S.children){const ae=S.children;for(let oe=0;oe<ae.length;oe++)r(ae[oe],P,a&&a.children[oe])}a=a||P}return L?()=>{i(L)}:nn}function i(g){if(ii(g)){const f=s.get(g);f&&(s.delete(g),n.splice(n.indexOf(f),1),f.children.forEach(i),f.alias.forEach(i))}else{const f=n.indexOf(g);f>-1&&(n.splice(f,1),g.record.name&&s.delete(g.record.name),g.children.forEach(i),g.alias.forEach(i))}}function l(){return n}function c(g){const f=wa(g,n);n.splice(f,0,g),g.record.name&&!Lo(g)&&s.set(g.record.name,g)}function m(g,f){let a,_={},S,I;if("name"in g&&g.name){if(a=s.get(g.name),!a)throw Ut(1,{location:g});I=a.record.name,_=G(ko(f.params,a.keys.filter(L=>!L.optional).concat(a.parent?a.parent.keys.filter(L=>L.optional):[]).map(L=>L.name)),g.params&&ko(g.params,a.keys.map(L=>L.name))),S=a.stringify(_)}else if(g.path!=null)S=g.path,a=n.find(L=>L.re.test(S)),a&&(_=a.parse(S),I=a.record.name);else{if(a=f.name?s.get(f.name):n.find(L=>L.re.test(f.path)),!a)throw Ut(1,{location:g,currentLocation:f});I=a.record.name,_=G({},f.params,g.params),S=a.stringify(_)}const E=[];let P=a;for(;P;)E.unshift(P.record),P=P.parent;return{name:I,path:S,params:_,matched:E,meta:ya(E)}}e.forEach(g=>r(g));function p(){n.length=0,s.clear()}return{addRoute:r,resolve:m,removeRoute:i,clearRoutes:p,getRoutes:l,getRecordMatcher:o}}function ko(e,t){const n={};for(const s of t)s in e&&(n[s]=e[s]);return n}function Do(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:ba(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function ba(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const s in e.components)t[s]=typeof n=="object"?n[s]:n;return t}function Lo(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function ya(e){return e.reduce((t,n)=>G(t,n.meta),{})}function No(e,t){const n={};for(const s in e)n[s]=s in t?t[s]:e[s];return n}function wa(e,t){let n=0,s=t.length;for(;n!==s;){const r=n+s>>1;ci(e,t[r])<0?s=r:n=r+1}const o=Ca(e);return o&&(s=t.lastIndexOf(o,s-1)),s}function Ca(e){let t=e;for(;t=t.parent;)if(ai(t)&&ci(e,t)===0)return t}function ai({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function xa(e){const t={};if(e===""||e==="?")return t;const s=(e[0]==="?"?e.slice(1):e).split("&");for(let o=0;o<s.length;++o){const r=s[o].replace(ei," "),i=r.indexOf("="),l=fn(i<0?r:r.slice(0,i)),c=i<0?null:fn(r.slice(i+1));if(l in t){let m=t[l];ke(m)||(m=t[l]=[m]),m.push(c)}else t[l]=c}return t}function Fo(e){let t="";for(let n in e){const s=e[n];if(n=Bc(n),s==null){s!==void 0&&(t+=(t.length?"&":"")+n);continue}(ke(s)?s.map(r=>r&&bs(r)):[s&&bs(s)]).forEach(r=>{r!==void 0&&(t+=(t.length?"&":"")+n,r!=null&&(t+="="+r))})}return t}function Sa(e){const t={};for(const n in e){const s=e[n];s!==void 0&&(t[n]=ke(s)?s.map(o=>o==null?null:""+o):s==null?s:""+s)}return t}const Ea=Symbol(""),Ho=Symbol(""),qn=Symbol(""),Ks=Symbol(""),ws=Symbol("");function Gt(){let e=[];function t(s){return e.push(s),()=>{const o=e.indexOf(s);o>-1&&e.splice(o,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function ft(e,t,n,s,o,r=i=>i()){const i=s&&(s.enterCallbacks[o]=s.enterCallbacks[o]||[]);return()=>new Promise((l,c)=>{const m=f=>{f===!1?c(Ut(4,{from:n,to:t})):f instanceof Error?c(f):aa(f)?c(Ut(2,{from:t,to:f})):(i&&s.enterCallbacks[o]===i&&typeof f=="function"&&i.push(f),l())},p=r(()=>e.call(s&&s.instances[o],t,n,m));let g=Promise.resolve(p);e.length<3&&(g=g.then(m)),g.catch(f=>c(f))})}function is(e,t,n,s,o=r=>r()){const r=[];for(const i of e)for(const l in i.components){let c=i.components[l];if(!(t!=="beforeRouteEnter"&&!i.instances[l]))if(Xr(c)){const p=(c.__vccOpts||c)[t];p&&r.push(ft(p,n,s,i,l,o))}else{let m=c();r.push(()=>m.then(p=>{if(!p)throw new Error(`Couldn't resolve component "${l}" at "${i.path}"`);const g=Mc(p)?p.default:p;i.mods[l]=p,i.components[l]=g;const a=(g.__vccOpts||g)[t];return a&&ft(a,n,s,i,l,o)()}))}}return r}function Vo(e){const t=Ie(qn),n=Ie(Ks),s=he(()=>{const c=xt(e.to);return t.resolve(c)}),o=he(()=>{const{matched:c}=s.value,{length:m}=c,p=c[m-1],g=n.matched;if(!p||!g.length)return-1;const f=g.findIndex(Vt.bind(null,p));if(f>-1)return f;const a=Uo(c[m-2]);return m>1&&Uo(p)===a&&g[g.length-1].path!==a?g.findIndex(Vt.bind(null,c[m-2])):f}),r=he(()=>o.value>-1&&Ta(n.params,s.value.params)),i=he(()=>o.value>-1&&o.value===n.matched.length-1&&oi(n.params,s.value.params));function l(c={}){if($a(c)){const m=t[xt(e.replace)?"replace":"push"](xt(e.to)).catch(nn);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>m),m}return Promise.resolve()}return{route:s,href:he(()=>s.value.href),isActive:r,isExactActive:i,navigate:l}}function Pa(e){return e.length===1?e[0]:e}const Ra=Pt({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Vo,setup(e,{slots:t}){const n=St(Vo(e)),{options:s}=Ie(qn),o=he(()=>({[jo(e.activeClass,s.linkActiveClass,"router-link-active")]:n.isActive,[jo(e.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const r=t.default&&Pa(t.default(n));return e.custom?r:Jr("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:o.value},r)}}}),Aa=Ra;function $a(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Ta(e,t){for(const n in t){const s=t[n],o=e[n];if(typeof s=="string"){if(s!==o)return!1}else if(!ke(o)||o.length!==s.length||s.some((r,i)=>r!==o[i]))return!1}return!0}function Uo(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const jo=(e,t,n)=>e??t??n,Oa=Pt({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const s=Ie(ws),o=he(()=>e.route||s.value),r=Ie(Ho,0),i=he(()=>{let m=xt(r);const{matched:p}=o.value;let g;for(;(g=p[m])&&!g.components;)m++;return m}),l=he(()=>o.value.matched[i.value]);Cn(Ho,he(()=>i.value+1)),Cn(Ea,l),Cn(ws,o);const c=de();return xn(()=>[c.value,l.value,e.name],([m,p,g],[f,a,_])=>{p&&(p.instances[g]=m,a&&a!==p&&m&&m===f&&(p.leaveGuards.size||(p.leaveGuards=a.leaveGuards),p.updateGuards.size||(p.updateGuards=a.updateGuards))),m&&p&&(!a||!Vt(p,a)||!f)&&(p.enterCallbacks[g]||[]).forEach(S=>S(m))},{flush:"post"}),()=>{const m=o.value,p=e.name,g=l.value,f=g&&g.components[p];if(!f)return Bo(n.default,{Component:f,route:m});const a=g.props[p],_=a?a===!0?m.params:typeof a=="function"?a(m):a:null,I=Jr(f,G({},_,t,{onVnodeUnmounted:E=>{E.component.isUnmounted&&(g.instances[p]=null)},ref:c}));return Bo(n.default,{Component:I,route:m})||I}}});function Bo(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const ui=Oa;function Ma(e){const t=_a(e.routes,e),n=e.parseQuery||xa,s=e.stringifyQuery||Fo,o=e.history,r=Gt(),i=Gt(),l=Gt(),c=Wi(ct);let m=ct;Mt&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const p=os.bind(null,y=>""+y),g=os.bind(null,Wc),f=os.bind(null,fn);function a(y,M){let T,D;return ii(y)?(T=t.getRecordMatcher(y),D=M):D=y,t.addRoute(D,T)}function _(y){const M=t.getRecordMatcher(y);M&&t.removeRoute(M)}function S(){return t.getRoutes().map(y=>y.record)}function I(y){return!!t.getRecordMatcher(y)}function E(y,M){if(M=G({},M||c.value),typeof y=="string"){const v=rs(n,y,M.path),b=t.resolve({path:v.path},M),C=o.createHref(v.fullPath);return G(v,b,{params:f(b.params),hash:fn(v.hash),redirectedFrom:void 0,href:C})}let T;if(y.path!=null)T=G({},y,{path:rs(n,y.path,M.path).path});else{const v=G({},y.params);for(const b in v)v[b]==null&&delete v[b];T=G({},y,{params:g(v)}),M.params=g(M.params)}const D=t.resolve(T,M),Z=y.hash||"";D.params=p(f(D.params));const u=zc(s,G({},y,{hash:jc(Z),path:D.path})),h=o.createHref(u);return G({fullPath:u,hash:Z,query:s===Fo?Sa(y.query):y.query||{}},D,{redirectedFrom:void 0,href:h})}function P(y){return typeof y=="string"?rs(n,y,c.value.path):G({},y)}function L(y,M){if(m!==y)return Ut(8,{from:M,to:y})}function k(y){return oe(y)}function X(y){return k(G(P(y),{replace:!0}))}function ae(y){const M=y.matched[y.matched.length-1];if(M&&M.redirect){const{redirect:T}=M;let D=typeof T=="function"?T(y):T;return typeof D=="string"&&(D=D.includes("?")||D.includes("#")?D=P(D):{path:D},D.params={}),G({query:y.query,hash:y.hash,params:D.path!=null?{}:y.params},D)}}function oe(y,M){const T=m=E(y),D=c.value,Z=y.state,u=y.force,h=y.replace===!0,v=ae(T);if(v)return oe(G(P(v),{state:typeof v=="object"?G({},Z,v.state):Z,force:u,replace:h}),M||T);const b=T;b.redirectedFrom=M;let C;return!u&&Jc(s,D,T)&&(C=Ut(16,{to:b,from:D}),Fe(D,D,!0,!1)),(C?Promise.resolve(C):Le(b,D)).catch(w=>Qe(w)?Qe(w,2)?w:lt(w):q(w,b,D)).then(w=>{if(w){if(Qe(w,2))return oe(G({replace:h},P(w.to),{state:typeof w.to=="object"?G({},Z,w.to.state):Z,force:u}),M||b)}else w=vt(b,D,!0,h,Z);return it(b,D,w),w})}function De(y,M){const T=L(y,M);return T?Promise.reject(T):Promise.resolve()}function rt(y){const M=$t.values().next().value;return M&&typeof M.runWithContext=="function"?M.runWithContext(y):y()}function Le(y,M){let T;const[D,Z,u]=Ia(y,M);T=is(D.reverse(),"beforeRouteLeave",y,M);for(const v of D)v.leaveGuards.forEach(b=>{T.push(ft(b,y,M))});const h=De.bind(null,y,M);return T.push(h),$e(T).then(()=>{T=[];for(const v of r.list())T.push(ft(v,y,M));return T.push(h),$e(T)}).then(()=>{T=is(Z,"beforeRouteUpdate",y,M);for(const v of Z)v.updateGuards.forEach(b=>{T.push(ft(b,y,M))});return T.push(h),$e(T)}).then(()=>{T=[];for(const v of u)if(v.beforeEnter)if(ke(v.beforeEnter))for(const b of v.beforeEnter)T.push(ft(b,y,M));else T.push(ft(v.beforeEnter,y,M));return T.push(h),$e(T)}).then(()=>(y.matched.forEach(v=>v.enterCallbacks={}),T=is(u,"beforeRouteEnter",y,M,rt),T.push(h),$e(T))).then(()=>{T=[];for(const v of i.list())T.push(ft(v,y,M));return T.push(h),$e(T)}).catch(v=>Qe(v,8)?v:Promise.reject(v))}function it(y,M,T){l.list().forEach(D=>rt(()=>D(y,M,T)))}function vt(y,M,T,D,Z){const u=L(y,M);if(u)return u;const h=M===ct,v=Mt?history.state:{};T&&(D||h?o.replace(y.fullPath,G({scroll:h&&v&&v.scroll},Z)):o.push(y.fullPath,Z)),c.value=y,Fe(y,M,T,h),lt()}let Ne;function Bt(){Ne||(Ne=o.listen((y,M,T)=>{if(!vn.listening)return;const D=E(y),Z=ae(D);if(Z){oe(G(Z,{replace:!0,force:!0}),D).catch(nn);return}m=D;const u=c.value;Mt&&sa($o(u.fullPath,T.delta),Wn()),Le(D,u).catch(h=>Qe(h,12)?h:Qe(h,2)?(oe(G(P(h.to),{force:!0}),D).then(v=>{Qe(v,20)&&!T.delta&&T.type===dn.pop&&o.go(-1,!1)}).catch(nn),Promise.reject()):(T.delta&&o.go(-T.delta,!1),q(h,D,u))).then(h=>{h=h||vt(D,u,!1),h&&(T.delta&&!Qe(h,8)?o.go(-T.delta,!1):T.type===dn.pop&&Qe(h,20)&&o.go(-1,!1)),it(D,u,h)}).catch(nn)}))}let Rt=Gt(),ce=Gt(),Y;function q(y,M,T){lt(y);const D=ce.list();return D.length?D.forEach(Z=>Z(y,M,T)):console.error(y),Promise.reject(y)}function ze(){return Y&&c.value!==ct?Promise.resolve():new Promise((y,M)=>{Rt.add([y,M])})}function lt(y){return Y||(Y=!y,Bt(),Rt.list().forEach(([M,T])=>y?T(y):M()),Rt.reset()),y}function Fe(y,M,T,D){const{scrollBehavior:Z}=e;if(!Mt||!Z)return Promise.resolve();const u=!T&&oa($o(y.fullPath,0))||(D||!T)&&history.state&&history.state.scroll||null;return Ls().then(()=>Z(y,M,u)).then(h=>h&&na(h)).catch(h=>q(h,y,M))}const ye=y=>o.go(y);let At;const $t=new Set,vn={currentRoute:c,listening:!0,addRoute:a,removeRoute:_,clearRoutes:t.clearRoutes,hasRoute:I,getRoutes:S,resolve:E,options:e,push:k,replace:X,go:ye,back:()=>ye(-1),forward:()=>ye(1),beforeEach:r.add,beforeResolve:i.add,afterEach:l.add,onError:ce.add,isReady:ze,install(y){const M=this;y.component("RouterLink",Aa),y.component("RouterView",ui),y.config.globalProperties.$router=M,Object.defineProperty(y.config.globalProperties,"$route",{enumerable:!0,get:()=>xt(c)}),Mt&&!At&&c.value===ct&&(At=!0,k(o.location).catch(Z=>{}));const T={};for(const Z in ct)Object.defineProperty(T,Z,{get:()=>c.value[Z],enumerable:!0});y.provide(qn,M),y.provide(Ks,dr(T)),y.provide(ws,c);const D=y.unmount;$t.add(y),y.unmount=function(){$t.delete(y),$t.size<1&&(m=ct,Ne&&Ne(),Ne=null,c.value=ct,At=!1,Y=!1),D()}}};function $e(y){return y.reduce((M,T)=>M.then(()=>rt(T)),Promise.resolve())}return vn}function Ia(e,t){const n=[],s=[],o=[],r=Math.max(t.matched.length,e.matched.length);for(let i=0;i<r;i++){const l=t.matched[i];l&&(e.matched.find(m=>Vt(m,l))?s.push(l):n.push(l));const c=e.matched[i];c&&(t.matched.find(m=>Vt(m,c))||o.push(c))}return[n,s,o]}function fi(){return Ie(qn)}function ka(e){return Ie(Ks)}const Da=Pt({__name:"App",setup(e){return(t,n)=>(j(),Kr(xt(ui)))}}),La={class:"mcp-marketplace"},Na={class:"header"},Fa={class:"container"},Ha={class:"header-content"},Va={class:"header-actions"},Ua={key:1,class:"user-menu"},ja={class:"search-section"},Ba={class:"container"},Ka={class:"search-bar"},Wa={class:"filters"},qa={class:"main-content"},Ga={class:"container"},za={class:"mcp-grid"},Ja=["onClick"],Qa={class:"mcp-header"},Ya=["src","alt"],Xa={class:"mcp-info"},Za={class:"mcp-name"},eu={class:"mcp-author"},tu={class:"mcp-description"},nu={class:"mcp-tags"},su={class:"mcp-stats"},ou={class:"stat"},ru={class:"stat"},iu={class:"stat"},lu={key:0,class:"load-more"},cu=["disabled"],au=Pt({__name:"MCPMarketplace",setup(e){const t=de(""),n=de(""),s=de("latest"),o=de([]),r=de(!1),i=de(!0),l=he(()=>!!localStorage.getItem("user_token")),c=he(()=>{let I=o.value;if(t.value){const E=t.value.toLowerCase();I=I.filter(P=>P.name.toLowerCase().includes(E)||P.description.toLowerCase().includes(E)||P.tags.some(L=>L.toLowerCase().includes(E)))}switch(n.value&&(I=I.filter(E=>E.category===n.value)),s.value){case"popular":I.sort((E,P)=>P.downloads-E.downloads);break;case"rating":I.sort((E,P)=>P.rating-E.rating);break;case"latest":default:I.sort((E,P)=>new Date(P.updatedAt).getTime()-new Date(E.updatedAt).getTime());break}return I}),m=()=>{},p=()=>{},g=()=>{},f=()=>{localStorage.removeItem("user_token"),localStorage.removeItem("user_info")},a=async()=>{r.value=!0,setTimeout(()=>{r.value=!1,i.value=!1},1e3)},_=I=>new Date(I).toLocaleDateString("zh-CN"),S=async()=>{r.value=!0,setTimeout(()=>{o.value=[{id:"1",name:"Weather MCP",description:"提供全球天气信息查询服务，支持实时天气、天气预报等功能",author:"WeatherCorp",icon:"/api/placeholder/64/64",tags:["天气","API","实用工具"],downloads:1250,rating:4.8,category:"api",createdAt:"2024-01-15",updatedAt:"2024-01-20"},{id:"2",name:"Database Query MCP",description:"数据库查询和管理工具，支持多种数据库类型",author:"DataTools",icon:"/api/placeholder/64/64",tags:["数据库","查询","管理"],downloads:890,rating:4.6,category:"data",createdAt:"2024-01-10",updatedAt:"2024-01-18"},{id:"3",name:"AI Text Processor",description:"AI 驱动的文本处理工具，支持文本分析、摘要生成等",author:"AILab",icon:"/api/placeholder/64/64",tags:["AI","文本处理","自然语言"],downloads:2100,rating:4.9,category:"ai",createdAt:"2024-01-05",updatedAt:"2024-01-22"}],r.value=!1},1e3)};return Hs(()=>{S()}),(I,E)=>(j(),K("div",La,[d("header",Na,[d("div",Fa,[d("div",Ha,[E[5]||(E[5]=d("h1",{class:"logo"},"MCP 广场",-1)),d("div",Va,[l.value?(j(),K("div",Ua,[d("button",{onClick:E[1]||(E[1]=P=>I.$router.push("/publish")),class:"btn btn-primary"}," 发布服务 "),d("button",{onClick:f,class:"btn btn-secondary"}," 退出登录 ")])):(j(),K("button",{key:0,onClick:E[0]||(E[0]=P=>I.$router.push("/login")),class:"btn btn-primary"}," 登录 "))])])])]),d("section",ja,[d("div",Ba,[d("div",Ka,[ie(d("input",{"onUpdate:modelValue":E[2]||(E[2]=P=>t.value=P),type:"text",placeholder:"搜索 MCP 服务...",class:"search-input",onInput:m},null,544),[[Se,t.value]]),E[6]||(E[6]=d("button",{class:"search-btn"},"搜索",-1))]),d("div",Wa,[ie(d("select",{"onUpdate:modelValue":E[3]||(E[3]=P=>n.value=P),onChange:p,class:"filter-select"},E[7]||(E[7]=[qr('<option value="" data-v-f4fb5955>所有分类</option><option value="ai" data-v-f4fb5955>AI 工具</option><option value="data" data-v-f4fb5955>数据处理</option><option value="api" data-v-f4fb5955>API 集成</option><option value="utility" data-v-f4fb5955>实用工具</option>',5)]),544),[[_s,n.value]]),ie(d("select",{"onUpdate:modelValue":E[4]||(E[4]=P=>s.value=P),onChange:g,class:"filter-select"},E[8]||(E[8]=[d("option",{value:"latest"},"最新发布",-1),d("option",{value:"popular"},"最受欢迎",-1),d("option",{value:"rating"},"评分最高",-1)]),544),[[_s,s.value]])])])]),d("main",qa,[d("div",Ga,[d("div",za,[(j(!0),K(pe,null,wt(c.value,P=>(j(),K("div",{key:P.id,class:"mcp-card",onClick:L=>I.$router.push(`/mcp/${P.id}`)},[d("div",Qa,[d("img",{src:P.icon,alt:P.name,class:"mcp-icon"},null,8,Ya),d("div",Xa,[d("h3",Za,W(P.name),1),d("p",eu,"by "+W(P.author),1)])]),d("p",tu,W(P.description),1),d("div",nu,[(j(!0),K(pe,null,wt(P.tags,L=>(j(),K("span",{key:L,class:"tag"},W(L),1))),128))]),d("div",su,[d("span",ou,[E[9]||(E[9]=d("i",{class:"icon-download"},null,-1)),le(" "+W(P.downloads),1)]),d("span",ru,[E[10]||(E[10]=d("i",{class:"icon-star"},null,-1)),le(" "+W(P.rating),1)]),d("span",iu,[E[11]||(E[11]=d("i",{class:"icon-calendar"},null,-1)),le(" "+W(_(P.updatedAt)),1)])])],8,Ja))),128))]),i.value?(j(),K("div",lu,[d("button",{onClick:a,class:"btn btn-secondary",disabled:r.value},W(r.value?"加载中...":"加载更多"),9,cu)])):Xe("",!0)])])]))}}),Gn=(e,t)=>{const n=e.__vccOpts||e;for(const[s,o]of t)n[s]=o;return n},uu=Gn(au,[["__scopeId","data-v-f4fb5955"]]),fu={class:"mcp-publish"},du={class:"header"},pu={class:"container"},hu={class:"header-content"},gu={class:"header-actions"},mu={class:"main-content"},vu={class:"container"},_u={class:"publish-form"},bu={class:"form-section"},yu={class:"form-group"},wu={class:"form-group"},Cu={class:"form-row"},xu={class:"form-group"},Su={class:"form-group"},Eu={class:"form-section"},Pu={class:"form-group"},Ru={class:"form-group"},Au={class:"form-group"},$u={key:0,class:"tags-preview"},Tu=["onClick"],Ou={class:"form-section"},Mu={class:"form-group"},Iu={class:"form-section"},ku={class:"form-group"},Du={class:"file-upload"},Lu={key:0,class:"icon-preview"},Nu=["src"],Fu={key:1,class:"upload-placeholder"},Hu={class:"form-actions"},Vu=["disabled"],Uu=["disabled"],ju=Pt({__name:"MCPPublish",setup(e){const t=fi(),n=St({name:"",description:"",category:"",version:"",repository:"",documentation:"",tags:[],mcpConfig:"",icon:null}),s=de(""),o=de(""),r=de(!1),i=()=>{const f=s.value.split(",").map(a=>a.trim()).filter(a=>a.length>0);n.tags=[...new Set(f)]},l=f=>{n.tags.splice(f,1),s.value=n.tags.join(", ")},c=f=>{const _=f.target.files?.[0];if(_){n.icon=_;const S=new FileReader;S.onload=I=>{o.value=I.target?.result},S.readAsDataURL(_)}},m=async()=>{console.log("保存草稿:",n),alert("草稿已保存")},p=async()=>{r.value=!0;try{try{JSON.parse(n.mcpConfig)}catch{alert("MCP 配置 JSON 格式不正确"),r.value=!1;return}console.log("发布服务:",n),await new Promise(f=>setTimeout(f,2e3)),alert("MCP 服务发布成功！"),t.push("/")}catch(f){console.error("发布失败:",f),alert("发布失败，请重试")}finally{r.value=!1}},g=()=>{localStorage.removeItem("user_token"),localStorage.removeItem("user_info"),t.push("/login")};return(f,a)=>(j(),K("div",fu,[d("header",du,[d("div",pu,[d("div",hu,[d("h1",{class:"logo",onClick:a[0]||(a[0]=_=>f.$router.push("/"))},"MCP 广场"),d("div",gu,[d("button",{onClick:a[1]||(a[1]=_=>f.$router.push("/")),class:"btn btn-secondary"}," 返回首页 "),d("button",{onClick:g,class:"btn btn-secondary"}," 退出登录 ")])])])]),d("main",mu,[d("div",vu,[d("div",_u,[a[27]||(a[27]=d("h2",null,"发布 MCP 服务",-1)),d("form",{onSubmit:Rn(p,["prevent"])},[d("div",bu,[a[16]||(a[16]=d("h3",null,"基本信息",-1)),d("div",yu,[a[11]||(a[11]=d("label",{for:"name"},"服务名称 *",-1)),ie(d("input",{id:"name","onUpdate:modelValue":a[2]||(a[2]=_=>n.name=_),type:"text",required:"",placeholder:"输入服务名称",class:"form-input"},null,512),[[Se,n.name]])]),d("div",wu,[a[12]||(a[12]=d("label",{for:"description"},"服务描述 *",-1)),ie(d("textarea",{id:"description","onUpdate:modelValue":a[3]||(a[3]=_=>n.description=_),required:"",placeholder:"详细描述您的 MCP 服务功能和用途",class:"form-textarea",rows:"4"},null,512),[[Se,n.description]])]),d("div",Cu,[d("div",xu,[a[14]||(a[14]=d("label",{for:"category"},"分类 *",-1)),ie(d("select",{id:"category","onUpdate:modelValue":a[4]||(a[4]=_=>n.category=_),required:"",class:"form-select"},a[13]||(a[13]=[qr('<option value="" data-v-e47ce5d6>选择分类</option><option value="ai" data-v-e47ce5d6>AI 工具</option><option value="data" data-v-e47ce5d6>数据处理</option><option value="api" data-v-e47ce5d6>API 集成</option><option value="utility" data-v-e47ce5d6>实用工具</option>',5)]),512),[[_s,n.category]])]),d("div",Su,[a[15]||(a[15]=d("label",{for:"version"},"版本号 *",-1)),ie(d("input",{id:"version","onUpdate:modelValue":a[5]||(a[5]=_=>n.version=_),type:"text",required:"",placeholder:"例如: 1.0.0",class:"form-input"},null,512),[[Se,n.version]])])])]),d("div",Eu,[a[20]||(a[20]=d("h3",null,"技术信息",-1)),d("div",Pu,[a[17]||(a[17]=d("label",{for:"repository"},"代码仓库 URL *",-1)),ie(d("input",{id:"repository","onUpdate:modelValue":a[6]||(a[6]=_=>n.repository=_),type:"url",required:"",placeholder:"https://github.com/username/repo",class:"form-input"},null,512),[[Se,n.repository]])]),d("div",Ru,[a[18]||(a[18]=d("label",{for:"documentation"},"文档链接",-1)),ie(d("input",{id:"documentation","onUpdate:modelValue":a[7]||(a[7]=_=>n.documentation=_),type:"url",placeholder:"https://docs.example.com",class:"form-input"},null,512),[[Se,n.documentation]])]),d("div",Au,[a[19]||(a[19]=d("label",{for:"tags"},"标签",-1)),ie(d("input",{id:"tags","onUpdate:modelValue":a[8]||(a[8]=_=>s.value=_),type:"text",placeholder:"输入标签，用逗号分隔",class:"form-input",onInput:i},null,544),[[Se,s.value]]),n.tags.length>0?(j(),K("div",$u,[(j(!0),K(pe,null,wt(n.tags,(_,S)=>(j(),K("span",{key:S,class:"tag"},[le(W(_)+" ",1),d("button",{type:"button",onClick:I=>l(S),class:"tag-remove"}," × ",8,Tu)]))),128))])):Xe("",!0)])]),d("div",Ou,[a[23]||(a[23]=d("h3",null,"MCP 配置",-1)),d("div",Mu,[a[21]||(a[21]=d("label",{for:"config"},"MCP 配置 JSON *",-1)),ie(d("textarea",{id:"config","onUpdate:modelValue":a[9]||(a[9]=_=>n.mcpConfig=_),required:"",placeholder:'{"name": "your-mcp", "version": "1.0.0", "description": "..."}',class:"form-textarea code-input",rows:"8"},null,512),[[Se,n.mcpConfig]]),a[22]||(a[22]=d("small",{class:"form-help"}," 请提供完整的 MCP 配置 JSON，包括服务端点、工具定义等 ",-1))])]),d("div",Iu,[a[26]||(a[26]=d("h3",null,"服务图标",-1)),d("div",ku,[a[25]||(a[25]=d("label",{for:"icon"},"上传图标",-1)),d("div",Du,[d("input",{id:"icon",type:"file",accept:"image/*",onChange:c,class:"file-input"},null,32),d("div",{class:"file-upload-area",onClick:a[10]||(a[10]=_=>f.$refs.iconInput?.click())},[o.value?(j(),K("div",Lu,[d("img",{src:o.value,alt:"图标预览"},null,8,Nu)])):(j(),K("div",Fu,a[24]||(a[24]=[d("i",{class:"icon-upload"},null,-1),d("p",null,"点击上传图标",-1),d("small",null,"支持 PNG, JPG, SVG 格式，建议尺寸 64x64px",-1)])))])])])]),d("div",Hu,[d("button",{type:"button",onClick:m,class:"btn btn-secondary",disabled:r.value}," 保存草稿 ",8,Vu),d("button",{type:"submit",class:"btn btn-primary",disabled:r.value},W(r.value?"发布中...":"发布服务"),9,Uu)])],32)])])])]))}}),Bu=Gn(ju,[["__scopeId","data-v-e47ce5d6"]]),Ku={class:"mcp-detail"},Wu={class:"header"},qu={class:"container"},Gu={class:"header-content"},zu={class:"header-actions"},Ju={key:0,class:"loading"},Qu={key:1,class:"main-content"},Yu={class:"container"},Xu={class:"service-header"},Zu={class:"service-info"},ef=["src","alt"],tf={class:"service-meta"},nf={class:"service-name"},sf={class:"service-author"},of={class:"service-stats"},rf={class:"stat"},lf={class:"stat"},cf={class:"stat"},af={class:"service-tags"},uf={class:"content-grid"},ff={class:"main-column"},df={class:"content-section"},pf={class:"service-description"},hf={class:"content-section"},gf={class:"install-guide"},mf={class:"code-block"},vf={class:"code-block"},_f={key:0,class:"content-section"},bf={class:"features-list"},yf={key:1,class:"content-section"},wf={class:"changelog"},Cf={class:"sidebar"},xf={class:"info-card"},Sf={class:"info-item"},Ef={class:"value"},Pf={class:"info-item"},Rf={class:"value"},Af={class:"info-item"},$f={class:"value"},Tf={class:"info-item"},Of={class:"value"},Mf={class:"info-card"},If={class:"links"},kf=["href"],Df=["href"],Lf=["href"],Nf={class:"info-card"},Ff={class:"author-info"},Hf=["src","alt"],Vf={class:"author-name"},Uf={class:"author-bio"},jf={key:2,class:"not-found"},Bf={class:"container"},Kf=Pt({__name:"MCPDetail",setup(e){const t=ka(),n=de(null),s=de(!0),o=he(()=>!!localStorage.getItem("user_token")),r=he(()=>n.value?`npm install -g ${n.value.name}`:""),i=he(()=>{if(!n.value)return"";try{const a=JSON.parse(n.value.mcpConfig);return JSON.stringify(a,null,2)}catch{return n.value.mcpConfig}}),l=a=>new Date(a).toLocaleDateString("zh-CN"),c=a=>({ai:"AI 工具",data:"数据处理",api:"API 集成",utility:"实用工具"})[a]||a,m=async()=>{try{await navigator.clipboard.writeText(r.value),alert("安装命令已复制到剪贴板")}catch(a){console.error("复制失败:",a)}},p=async()=>{try{await navigator.clipboard.writeText(i.value),alert("配置已复制到剪贴板")}catch(a){console.error("复制失败:",a)}},g=()=>{if(!n.value)return;const a=new Blob([i.value],{type:"application/json"}),_=URL.createObjectURL(a),S=document.createElement("a");S.href=_,S.download=`${n.value.name}-config.json`,document.body.appendChild(S),S.click(),document.body.removeChild(S),URL.revokeObjectURL(_)},f=async()=>{s.value=!0;const a=t.params.id;setTimeout(()=>{a==="1"?n.value={id:"1",name:"Weather MCP",description:"提供全球天气信息查询服务，支持实时天气、天气预报、历史天气数据查询等功能。该服务集成了多个权威天气数据源，确保数据的准确性和实时性。",author:"WeatherCorp",authorBio:"专注于气象数据服务的技术团队",authorAvatar:"/api/placeholder/48/48",icon:"/api/placeholder/64/64",tags:["天气","API","实用工具"],downloads:1250,rating:4.8,category:"api",version:"1.2.0",license:"MIT",createdAt:"2024-01-15",updatedAt:"2024-01-20",repository:"https://github.com/weathercorp/weather-mcp",documentation:"https://docs.weathercorp.com/mcp",homepage:"https://weathercorp.com",mcpConfig:JSON.stringify({name:"weather-mcp",version:"1.2.0",description:"Weather information service",tools:[{name:"get_weather",description:"Get current weather for a location"},{name:"get_forecast",description:"Get weather forecast for a location"}]},null,2),features:["实时天气数据查询","7天天气预报","历史天气数据","多种天气指标支持","全球城市覆盖","多语言支持"],changelog:[{version:"1.2.0",date:"2024-01-20",changes:["新增历史天气数据查询功能","优化数据缓存机制","修复部分城市数据不准确的问题"]},{version:"1.1.0",date:"2024-01-18",changes:["新增7天天气预报","支持更多天气指标","改进错误处理"]}]}:n.value=null,s.value=!1},1e3)};return Hs(()=>{f()}),(a,_)=>(j(),K("div",Ku,[d("header",Wu,[d("div",qu,[d("div",Gu,[d("h1",{class:"logo",onClick:_[0]||(_[0]=S=>a.$router.push("/"))},"MCP 广场"),d("div",zu,[d("button",{onClick:_[1]||(_[1]=S=>a.$router.push("/")),class:"btn btn-secondary"}," 返回首页 "),o.value?(j(),K("button",{key:0,onClick:_[2]||(_[2]=S=>a.$router.push("/publish")),class:"btn btn-primary"}," 发布服务 ")):Xe("",!0)])])])]),s.value?(j(),K("div",Ju,_[4]||(_[4]=[d("div",{class:"container"},[d("p",null,"加载中...")],-1)]))):n.value?(j(),K("main",Qu,[d("div",Yu,[d("div",Xu,[d("div",Zu,[d("img",{src:n.value.icon,alt:n.value.name,class:"service-icon"},null,8,ef),d("div",tf,[d("h1",nf,W(n.value.name),1),d("p",sf,"by "+W(n.value.author),1),d("div",of,[d("span",rf,[_[5]||(_[5]=d("i",{class:"icon-download"},null,-1)),le(" "+W(n.value.downloads)+" 下载 ",1)]),d("span",lf,[_[6]||(_[6]=d("i",{class:"icon-star"},null,-1)),le(" "+W(n.value.rating)+" 评分 ",1)]),d("span",cf,[_[7]||(_[7]=d("i",{class:"icon-calendar"},null,-1)),le(" 更新于 "+W(l(n.value.updatedAt)),1)])])])]),d("div",{class:"service-actions"},[d("button",{onClick:m,class:"btn btn-primary btn-large"},_[8]||(_[8]=[d("i",{class:"icon-copy"},null,-1),le(" 复制安装命令 ")])),d("button",{onClick:g,class:"btn btn-secondary"},_[9]||(_[9]=[d("i",{class:"icon-download"},null,-1),le(" 下载配置 ")]))])]),d("div",af,[(j(!0),K(pe,null,wt(n.value.tags,S=>(j(),K("span",{key:S,class:"tag"},W(S),1))),128))]),d("div",uf,[d("div",ff,[d("section",df,[_[10]||(_[10]=d("h2",null,"服务描述",-1)),d("p",pf,W(n.value.description),1)]),d("section",hf,[_[16]||(_[16]=d("h2",null,"安装使用",-1)),d("div",gf,[_[11]||(_[11]=d("h3",null,"1. 安装 MCP 服务",-1)),d("div",mf,[d("code",null,W(r.value),1),d("button",{onClick:m,class:"copy-btn"},"复制")]),_[12]||(_[12]=d("h3",null,"2. 配置 Claude Desktop",-1)),_[13]||(_[13]=d("p",null,"将以下配置添加到您的 Claude Desktop 配置文件中：",-1)),d("div",vf,[d("pre",null,[d("code",null,W(i.value),1)]),d("button",{onClick:p,class:"copy-btn"},"复制")]),_[14]||(_[14]=d("h3",null,"3. 重启 Claude Desktop",-1)),_[15]||(_[15]=d("p",null,"保存配置后，重启 Claude Desktop 即可使用该 MCP 服务。",-1))])]),n.value.features?(j(),K("section",_f,[_[17]||(_[17]=d("h2",null,"功能特性",-1)),d("ul",bf,[(j(!0),K(pe,null,wt(n.value.features,S=>(j(),K("li",{key:S},W(S),1))),128))])])):Xe("",!0),n.value.changelog?(j(),K("section",yf,[_[18]||(_[18]=d("h2",null,"更新日志",-1)),d("div",wf,[(j(!0),K(pe,null,wt(n.value.changelog,S=>(j(),K("div",{key:S.version,class:"changelog-item"},[d("h4",null,W(S.version)+" - "+W(l(S.date)),1),d("ul",null,[(j(!0),K(pe,null,wt(S.changes,I=>(j(),K("li",{key:I},W(I),1))),128))])]))),128))])])):Xe("",!0)]),d("div",Cf,[d("div",xf,[_[23]||(_[23]=d("h3",null,"基本信息",-1)),d("div",Sf,[_[19]||(_[19]=d("span",{class:"label"},"版本:",-1)),d("span",Ef,W(n.value.version),1)]),d("div",Pf,[_[20]||(_[20]=d("span",{class:"label"},"分类:",-1)),d("span",Rf,W(c(n.value.category)),1)]),d("div",Af,[_[21]||(_[21]=d("span",{class:"label"},"许可证:",-1)),d("span",$f,W(n.value.license||"MIT"),1)]),d("div",Tf,[_[22]||(_[22]=d("span",{class:"label"},"发布时间:",-1)),d("span",Of,W(l(n.value.createdAt)),1)])]),d("div",Mf,[_[27]||(_[27]=d("h3",null,"相关链接",-1)),d("div",If,[n.value.repository?(j(),K("a",{key:0,href:n.value.repository,target:"_blank",class:"link-item"},_[24]||(_[24]=[d("i",{class:"icon-github"},null,-1),le(" 源代码仓库 ")]),8,kf)):Xe("",!0),n.value.documentation?(j(),K("a",{key:1,href:n.value.documentation,target:"_blank",class:"link-item"},_[25]||(_[25]=[d("i",{class:"icon-book"},null,-1),le(" 使用文档 ")]),8,Df)):Xe("",!0),n.value.homepage?(j(),K("a",{key:2,href:n.value.homepage,target:"_blank",class:"link-item"},_[26]||(_[26]=[d("i",{class:"icon-home"},null,-1),le(" 项目主页 ")]),8,Lf)):Xe("",!0)])]),d("div",Nf,[_[28]||(_[28]=d("h3",null,"作者信息",-1)),d("div",Ff,[d("img",{src:n.value.authorAvatar,alt:n.value.author,class:"author-avatar"},null,8,Hf),d("div",null,[d("p",Vf,W(n.value.author),1),d("p",Uf,W(n.value.authorBio),1)])])])])])])])):(j(),K("div",jf,[d("div",Bf,[_[29]||(_[29]=d("h2",null,"服务未找到",-1)),_[30]||(_[30]=d("p",null,"抱歉，您访问的 MCP 服务不存在或已被删除。",-1)),d("button",{onClick:_[3]||(_[3]=S=>a.$router.push("/")),class:"btn btn-primary"}," 返回首页 ")])]))]))}}),Wf=Gn(Kf,[["__scopeId","data-v-0c9a0ae4"]]),qf={class:"login-page"},Gf={class:"login-container"},zf={class:"login-card"},Jf={class:"login-header"},Qf={class:"form-group"},Yf=["disabled"],Xf={class:"form-group"},Zf=["disabled"],ed={class:"form-options"},td={class:"checkbox-label"},nd=["disabled"],sd=["disabled"],od={class:"social-login"},rd=["disabled"],id=["disabled"],ld={class:"signup-link"},cd={class:"modal-header"},ad={class:"form-group"},ud={class:"form-group"},fd={class:"form-group"},dd={class:"form-group"},pd={class:"form-group"},hd={class:"checkbox-label"},gd=["disabled"],md=Pt({__name:"Login",setup(e){const t=fi(),n=de(!1),s=de(!1),o=de(!1),r=St({email:"",password:"",rememberMe:!1}),i=St({name:"",email:"",password:"",confirmPassword:"",agreeTerms:!1}),l=async()=>{n.value=!0;try{console.log("登录信息:",r),await new Promise(_=>setTimeout(_,1500));const g="mock_jwt_token_"+Date.now(),f={id:"1",name:"Test User",email:r.email,avatar:"/api/placeholder/48/48"};localStorage.setItem("user_token",g),localStorage.setItem("user_info",JSON.stringify(f));const a=t.currentRoute.value.query.redirect;t.push(a||"/")}catch(g){console.error("登录失败:",g),alert("登录失败，请检查邮箱和密码")}finally{n.value=!1}},c=async()=>{if(i.password!==i.confirmPassword){alert("两次输入的密码不一致");return}s.value=!0;try{console.log("注册信息:",i),await new Promise(g=>setTimeout(g,1500)),alert("注册成功！请登录您的账户"),o.value=!1,r.email=i.email}catch(g){console.error("注册失败:",g),alert("注册失败，请重试")}finally{s.value=!1}},m=async()=>{n.value=!0;try{console.log("GitHub 登录"),await new Promise(a=>setTimeout(a,1e3));const g="github_token_"+Date.now(),f={id:"2",name:"GitHub User",email:"<EMAIL>",avatar:"/api/placeholder/48/48"};localStorage.setItem("user_token",g),localStorage.setItem("user_info",JSON.stringify(f)),t.push("/")}catch(g){console.error("GitHub 登录失败:",g),alert("GitHub 登录失败，请重试")}finally{n.value=!1}},p=async()=>{n.value=!0;try{console.log("Google 登录"),await new Promise(a=>setTimeout(a,1e3));const g="google_token_"+Date.now(),f={id:"3",name:"Google User",email:"<EMAIL>",avatar:"/api/placeholder/48/48"};localStorage.setItem("user_token",g),localStorage.setItem("user_info",JSON.stringify(f)),t.push("/")}catch(g){console.error("Google 登录失败:",g),alert("Google 登录失败，请重试")}finally{n.value=!1}};return(g,f)=>(j(),K("div",qf,[d("div",Gf,[d("div",zf,[d("div",Jf,[d("h1",{class:"logo",onClick:f[0]||(f[0]=a=>g.$router.push("/"))},"MCP 广场"),f[13]||(f[13]=d("h2",null,"登录账户",-1)),f[14]||(f[14]=d("p",null,"登录后即可发布和管理您的 MCP 服务",-1))]),d("form",{onSubmit:Rn(l,["prevent"]),class:"login-form"},[d("div",Qf,[f[15]||(f[15]=d("label",{for:"email"},"邮箱地址",-1)),ie(d("input",{id:"email","onUpdate:modelValue":f[1]||(f[1]=a=>r.email=a),type:"email",required:"",placeholder:"输入您的邮箱地址",class:"form-input",disabled:n.value},null,8,Yf),[[Se,r.email]])]),d("div",Xf,[f[16]||(f[16]=d("label",{for:"password"},"密码",-1)),ie(d("input",{id:"password","onUpdate:modelValue":f[2]||(f[2]=a=>r.password=a),type:"password",required:"",placeholder:"输入您的密码",class:"form-input",disabled:n.value},null,8,Zf),[[Se,r.password]])]),d("div",ed,[d("label",td,[ie(d("input",{"onUpdate:modelValue":f[3]||(f[3]=a=>r.rememberMe=a),type:"checkbox",disabled:n.value},null,8,nd),[[Co,r.rememberMe]]),f[17]||(f[17]=le(" 记住我 "))]),f[18]||(f[18]=d("a",{href:"#",class:"forgot-link"},"忘记密码？",-1))]),d("button",{type:"submit",class:"login-btn",disabled:n.value},W(n.value?"登录中...":"登录"),9,sd),f[22]||(f[22]=d("div",{class:"divider"},[d("span",null,"或")],-1)),d("div",od,[d("button",{type:"button",onClick:m,class:"social-btn github-btn",disabled:n.value},f[19]||(f[19]=[d("i",{class:"icon-github"},null,-1),le(" 使用 GitHub 登录 ")]),8,rd),d("button",{type:"button",onClick:p,class:"social-btn google-btn",disabled:n.value},f[20]||(f[20]=[d("i",{class:"icon-google"},null,-1),le(" 使用 Google 登录 ")]),8,id)]),d("div",ld,[d("p",null,[f[21]||(f[21]=le("还没有账户？ ")),d("a",{href:"#",onClick:f[4]||(f[4]=a=>o.value=!0)},"立即注册")])])],32)])]),o.value?(j(),K("div",{key:0,class:"modal-overlay",onClick:f[12]||(f[12]=a=>o.value=!1)},[d("div",{class:"modal-content",onClick:f[11]||(f[11]=Rn(()=>{},["stop"]))},[d("div",cd,[f[23]||(f[23]=d("h3",null,"注册账户",-1)),d("button",{onClick:f[5]||(f[5]=a=>o.value=!1),class:"close-btn"},"×")]),d("form",{onSubmit:Rn(c,["prevent"]),class:"register-form"},[d("div",ad,[f[24]||(f[24]=d("label",{for:"reg-name"},"用户名",-1)),ie(d("input",{id:"reg-name","onUpdate:modelValue":f[6]||(f[6]=a=>i.name=a),type:"text",required:"",placeholder:"输入用户名",class:"form-input"},null,512),[[Se,i.name]])]),d("div",ud,[f[25]||(f[25]=d("label",{for:"reg-email"},"邮箱地址",-1)),ie(d("input",{id:"reg-email","onUpdate:modelValue":f[7]||(f[7]=a=>i.email=a),type:"email",required:"",placeholder:"输入邮箱地址",class:"form-input"},null,512),[[Se,i.email]])]),d("div",fd,[f[26]||(f[26]=d("label",{for:"reg-password"},"密码",-1)),ie(d("input",{id:"reg-password","onUpdate:modelValue":f[8]||(f[8]=a=>i.password=a),type:"password",required:"",placeholder:"输入密码（至少6位）",class:"form-input",minlength:"6"},null,512),[[Se,i.password]])]),d("div",dd,[f[27]||(f[27]=d("label",{for:"reg-confirm"},"确认密码",-1)),ie(d("input",{id:"reg-confirm","onUpdate:modelValue":f[9]||(f[9]=a=>i.confirmPassword=a),type:"password",required:"",placeholder:"再次输入密码",class:"form-input"},null,512),[[Se,i.confirmPassword]])]),d("div",pd,[d("label",hd,[ie(d("input",{"onUpdate:modelValue":f[10]||(f[10]=a=>i.agreeTerms=a),type:"checkbox",required:""},null,512),[[Co,i.agreeTerms]]),f[28]||(f[28]=le(" 我同意 ")),f[29]||(f[29]=d("a",{href:"#",target:"_blank"},"服务条款",-1)),f[30]||(f[30]=le(" 和 ")),f[31]||(f[31]=d("a",{href:"#",target:"_blank"},"隐私政策",-1))])]),d("button",{type:"submit",class:"register-btn",disabled:s.value},W(s.value?"注册中...":"注册"),9,gd)],32)])])):Xe("",!0)]))}}),vd=Gn(md,[["__scopeId","data-v-b12069c9"]]),di=Ma({history:ca("/"),routes:[{path:"/",name:"marketplace",component:uu},{path:"/publish",name:"publish",component:Bu,meta:{requiresAuth:!0}},{path:"/mcp/:id",name:"detail",component:Wf},{path:"/login",name:"login",component:vd}]});di.beforeEach((e,t,n)=>{const s=localStorage.getItem("user_token");e.meta.requiresAuth&&!s?n("/login"):n()});const zn=Rc(Da),_d=Oc();zn.use(_d);zn.use(di);zn.config.errorHandler=(e,t,n)=>{console.error("全局错误:",e),console.error("错误信息:",n)};zn.mount("#app");
