<template>
  <div class="mcp-marketplace">
    <!-- 头部导航 -->
    <header class="header">
      <div class="container">
        <div class="header-content">
          <h1 class="logo">MCP 广场</h1>
          <div class="header-actions">
            <button v-if="!isLoggedIn" @click="$router.push('/login')" class="btn btn-primary">
              登录
            </button>
            <div v-else class="user-menu">
              <button @click="$router.push('/publish')" class="btn btn-primary">
                发布服务
              </button>
              <button @click="logout" class="btn btn-secondary">
                退出登录
              </button>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- 搜索和筛选 -->
    <section class="search-section">
      <div class="container">
        <div class="search-bar">
          <input 
            v-model="searchQuery" 
            type="text" 
            placeholder="搜索 MCP 服务..." 
            class="search-input"
            @input="handleSearch"
          />
          <button class="search-btn">搜索</button>
        </div>
        
        <div class="filters">
          <select v-model="selectedCategory" @change="handleFilter" class="filter-select">
            <option value="">所有分类</option>
            <option value="ai">AI 工具</option>
            <option value="data">数据处理</option>
            <option value="api">API 集成</option>
            <option value="utility">实用工具</option>
          </select>
          
          <select v-model="sortBy" @change="handleSort" class="filter-select">
            <option value="latest">最新发布</option>
            <option value="popular">最受欢迎</option>
            <option value="rating">评分最高</option>
          </select>
        </div>
      </div>
    </section>

    <!-- MCP 服务列表 -->
    <main class="main-content">
      <div class="container">
        <div class="mcp-grid">
          <div 
            v-for="mcp in filteredMCPs" 
            :key="mcp.id" 
            class="mcp-card"
            @click="$router.push(`/mcp/${mcp.id}`)"
          >
            <div class="mcp-header">
              <img :src="mcp.icon" :alt="mcp.name" class="mcp-icon" />
              <div class="mcp-info">
                <h3 class="mcp-name">{{ mcp.name }}</h3>
                <p class="mcp-author">by {{ mcp.author }}</p>
              </div>
            </div>
            
            <p class="mcp-description">{{ mcp.description }}</p>
            
            <div class="mcp-tags">
              <span 
                v-for="tag in mcp.tags" 
                :key="tag" 
                class="tag"
              >
                {{ tag }}
              </span>
            </div>
            
            <div class="mcp-stats">
              <span class="stat">
                <i class="icon-download"></i>
                {{ mcp.downloads }}
              </span>
              <span class="stat">
                <i class="icon-star"></i>
                {{ mcp.rating }}
              </span>
              <span class="stat">
                <i class="icon-calendar"></i>
                {{ formatDate(mcp.updatedAt) }}
              </span>
            </div>
          </div>
        </div>
        
        <!-- 加载更多 -->
        <div v-if="hasMore" class="load-more">
          <button @click="loadMore" class="btn btn-secondary" :disabled="loading">
            {{ loading ? '加载中...' : '加载更多' }}
          </button>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

interface MCPService {
  id: string
  name: string
  description: string
  author: string
  icon: string
  tags: string[]
  downloads: number
  rating: number
  category: string
  createdAt: string
  updatedAt: string
}

// 响应式数据
const searchQuery = ref('')
const selectedCategory = ref('')
const sortBy = ref('latest')
const mcpServices = ref<MCPService[]>([])
const loading = ref(false)
const hasMore = ref(true)

// 计算属性
const isLoggedIn = computed(() => {
  return !!localStorage.getItem('user_token')
})

const filteredMCPs = computed(() => {
  let filtered = mcpServices.value

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(mcp => 
      mcp.name.toLowerCase().includes(query) ||
      mcp.description.toLowerCase().includes(query) ||
      mcp.tags.some(tag => tag.toLowerCase().includes(query))
    )
  }

  // 分类过滤
  if (selectedCategory.value) {
    filtered = filtered.filter(mcp => mcp.category === selectedCategory.value)
  }

  // 排序
  switch (sortBy.value) {
    case 'popular':
      filtered.sort((a, b) => b.downloads - a.downloads)
      break
    case 'rating':
      filtered.sort((a, b) => b.rating - a.rating)
      break
    case 'latest':
    default:
      filtered.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
      break
  }

  return filtered
})

// 方法
const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const handleFilter = () => {
  // 筛选逻辑已在计算属性中处理
}

const handleSort = () => {
  // 排序逻辑已在计算属性中处理
}

const logout = () => {
  localStorage.removeItem('user_token')
  localStorage.removeItem('user_info')
  // 可以添加其他清理逻辑
}

const loadMore = async () => {
  loading.value = true
  // 模拟加载更多数据
  setTimeout(() => {
    loading.value = false
    hasMore.value = false // 示例中设置为没有更多数据
  }, 1000)
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

// 模拟数据加载
const loadMCPServices = async () => {
  loading.value = true
  
  // 模拟 API 调用
  setTimeout(() => {
    mcpServices.value = [
      {
        id: '1',
        name: 'Weather MCP',
        description: '提供全球天气信息查询服务，支持实时天气、天气预报等功能',
        author: 'WeatherCorp',
        icon: '/api/placeholder/64/64',
        tags: ['天气', 'API', '实用工具'],
        downloads: 1250,
        rating: 4.8,
        category: 'api',
        createdAt: '2024-01-15',
        updatedAt: '2024-01-20'
      },
      {
        id: '2',
        name: 'Database Query MCP',
        description: '数据库查询和管理工具，支持多种数据库类型',
        author: 'DataTools',
        icon: '/api/placeholder/64/64',
        tags: ['数据库', '查询', '管理'],
        downloads: 890,
        rating: 4.6,
        category: 'data',
        createdAt: '2024-01-10',
        updatedAt: '2024-01-18'
      },
      {
        id: '3',
        name: 'AI Text Processor',
        description: 'AI 驱动的文本处理工具，支持文本分析、摘要生成等',
        author: 'AILab',
        icon: '/api/placeholder/64/64',
        tags: ['AI', '文本处理', '自然语言'],
        downloads: 2100,
        rating: 4.9,
        category: 'ai',
        createdAt: '2024-01-05',
        updatedAt: '2024-01-22'
      }
    ]
    loading.value = false
  }, 1000)
}

onMounted(() => {
  loadMCPServices()
})
</script>

<style scoped>
.mcp-marketplace {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  background: white;
  border-bottom: 1px solid #e0e0e0;
  padding: 1rem 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-size: 1.5rem;
  font-weight: bold;
  color: #333;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.user-menu {
  display: flex;
  gap: 0.5rem;
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #545b62;
}

.search-section {
  background: white;
  padding: 2rem 0;
  border-bottom: 1px solid #e0e0e0;
}

.search-bar {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.search-input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.search-btn {
  padding: 0.75rem 1.5rem;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.filters {
  display: flex;
  gap: 1rem;
}

.filter-select {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
}

.main-content {
  padding: 2rem 0;
}

.mcp-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.mcp-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.mcp-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.mcp-header {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.mcp-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  margin-right: 1rem;
  background-color: #f0f0f0;
}

.mcp-name {
  font-size: 1.1rem;
  font-weight: bold;
  margin: 0 0 0.25rem 0;
  color: #333;
}

.mcp-author {
  font-size: 0.9rem;
  color: #666;
  margin: 0;
}

.mcp-description {
  color: #555;
  line-height: 1.5;
  margin-bottom: 1rem;
}

.mcp-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.tag {
  background-color: #e9ecef;
  color: #495057;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
}

.mcp-stats {
  display: flex;
  justify-content: space-between;
  font-size: 0.9rem;
  color: #666;
}

.stat {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.load-more {
  text-align: center;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>
