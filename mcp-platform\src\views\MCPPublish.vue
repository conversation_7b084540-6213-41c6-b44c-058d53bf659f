<template>
  <div class="mcp-publish">
    <!-- 头部导航 -->
    <header class="header">
      <div class="container">
        <div class="header-content">
          <h1 class="logo" @click="$router.push('/')">MCP 广场</h1>
          <div class="header-actions">
            <button @click="$router.push('/')" class="btn btn-secondary">
              返回首页
            </button>
            <button @click="logout" class="btn btn-secondary">
              退出登录
            </button>
          </div>
        </div>
      </div>
    </header>

    <!-- 发布表单 -->
    <main class="main-content">
      <div class="container">
        <div class="publish-form">
          <h2>发布 MCP 服务</h2>
          
          <form @submit.prevent="handleSubmit">
            <!-- 基本信息 -->
            <div class="form-section">
              <h3>基本信息</h3>
              
              <div class="form-group">
                <label for="name">服务名称 *</label>
                <input 
                  id="name"
                  v-model="form.name" 
                  type="text" 
                  required 
                  placeholder="输入服务名称"
                  class="form-input"
                />
              </div>
              
              <div class="form-group">
                <label for="description">服务描述 *</label>
                <textarea 
                  id="description"
                  v-model="form.description" 
                  required 
                  placeholder="详细描述您的 MCP 服务功能和用途"
                  class="form-textarea"
                  rows="4"
                ></textarea>
              </div>
              
              <div class="form-row">
                <div class="form-group">
                  <label for="category">分类 *</label>
                  <select id="category" v-model="form.category" required class="form-select">
                    <option value="">选择分类</option>
                    <option value="ai">AI 工具</option>
                    <option value="data">数据处理</option>
                    <option value="api">API 集成</option>
                    <option value="utility">实用工具</option>
                  </select>
                </div>
                
                <div class="form-group">
                  <label for="version">版本号 *</label>
                  <input 
                    id="version"
                    v-model="form.version" 
                    type="text" 
                    required 
                    placeholder="例如: 1.0.0"
                    class="form-input"
                  />
                </div>
              </div>
            </div>

            <!-- 技术信息 -->
            <div class="form-section">
              <h3>技术信息</h3>
              
              <div class="form-group">
                <label for="repository">代码仓库 URL *</label>
                <input 
                  id="repository"
                  v-model="form.repository" 
                  type="url" 
                  required 
                  placeholder="https://github.com/username/repo"
                  class="form-input"
                />
              </div>
              
              <div class="form-group">
                <label for="documentation">文档链接</label>
                <input 
                  id="documentation"
                  v-model="form.documentation" 
                  type="url" 
                  placeholder="https://docs.example.com"
                  class="form-input"
                />
              </div>
              
              <div class="form-group">
                <label for="tags">标签</label>
                <input 
                  id="tags"
                  v-model="tagsInput" 
                  type="text" 
                  placeholder="输入标签，用逗号分隔"
                  class="form-input"
                  @input="updateTags"
                />
                <div v-if="form.tags.length > 0" class="tags-preview">
                  <span 
                    v-for="(tag, index) in form.tags" 
                    :key="index" 
                    class="tag"
                  >
                    {{ tag }}
                    <button 
                      type="button" 
                      @click="removeTag(index)"
                      class="tag-remove"
                    >
                      ×
                    </button>
                  </span>
                </div>
              </div>
            </div>

            <!-- 配置信息 -->
            <div class="form-section">
              <h3>MCP 配置</h3>
              
              <div class="form-group">
                <label for="config">MCP 配置 JSON *</label>
                <textarea 
                  id="config"
                  v-model="form.mcpConfig" 
                  required 
                  placeholder='{"name": "your-mcp", "version": "1.0.0", "description": "..."}'
                  class="form-textarea code-input"
                  rows="8"
                ></textarea>
                <small class="form-help">
                  请提供完整的 MCP 配置 JSON，包括服务端点、工具定义等
                </small>
              </div>
            </div>

            <!-- 图标上传 -->
            <div class="form-section">
              <h3>服务图标</h3>
              
              <div class="form-group">
                <label for="icon">上传图标</label>
                <div class="file-upload">
                  <input 
                    id="icon"
                    type="file" 
                    accept="image/*"
                    @change="handleIconUpload"
                    class="file-input"
                  />
                  <div class="file-upload-area" @click="$refs.iconInput?.click()">
                    <div v-if="iconPreview" class="icon-preview">
                      <img :src="iconPreview" alt="图标预览" />
                    </div>
                    <div v-else class="upload-placeholder">
                      <i class="icon-upload"></i>
                      <p>点击上传图标</p>
                      <small>支持 PNG, JPG, SVG 格式，建议尺寸 64x64px</small>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 提交按钮 -->
            <div class="form-actions">
              <button type="button" @click="saveDraft" class="btn btn-secondary" :disabled="submitting">
                保存草稿
              </button>
              <button type="submit" class="btn btn-primary" :disabled="submitting">
                {{ submitting ? '发布中...' : '发布服务' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 表单数据
const form = reactive({
  name: '',
  description: '',
  category: '',
  version: '',
  repository: '',
  documentation: '',
  tags: [] as string[],
  mcpConfig: '',
  icon: null as File | null
})

const tagsInput = ref('')
const iconPreview = ref('')
const submitting = ref(false)

// 方法
const updateTags = () => {
  const tags = tagsInput.value
    .split(',')
    .map(tag => tag.trim())
    .filter(tag => tag.length > 0)
  form.tags = [...new Set(tags)] // 去重
}

const removeTag = (index: number) => {
  form.tags.splice(index, 1)
  tagsInput.value = form.tags.join(', ')
}

const handleIconUpload = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  
  if (file) {
    form.icon = file
    
    // 创建预览
    const reader = new FileReader()
    reader.onload = (e) => {
      iconPreview.value = e.target?.result as string
    }
    reader.readAsDataURL(file)
  }
}

const saveDraft = async () => {
  // 保存草稿逻辑
  console.log('保存草稿:', form)
  alert('草稿已保存')
}

const handleSubmit = async () => {
  submitting.value = true
  
  try {
    // 验证 MCP 配置 JSON
    try {
      JSON.parse(form.mcpConfig)
    } catch (error) {
      alert('MCP 配置 JSON 格式不正确')
      submitting.value = false
      return
    }
    
    // 模拟 API 调用
    console.log('发布服务:', form)
    
    // 模拟上传过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    alert('MCP 服务发布成功！')
    router.push('/')
    
  } catch (error) {
    console.error('发布失败:', error)
    alert('发布失败，请重试')
  } finally {
    submitting.value = false
  }
}

const logout = () => {
  localStorage.removeItem('user_token')
  localStorage.removeItem('user_info')
  router.push('/login')
}
</script>

<style scoped>
.mcp-publish {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  background: white;
  border-bottom: 1px solid #e0e0e0;
  padding: 1rem 0;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 1rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-size: 1.5rem;
  font-weight: bold;
  color: #333;
  margin: 0;
  cursor: pointer;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #545b62;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.main-content {
  padding: 2rem 0;
}

.publish-form {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.publish-form h2 {
  margin: 0 0 2rem 0;
  color: #333;
}

.form-section {
  margin-bottom: 2rem;
}

.form-section h3 {
  margin: 0 0 1rem 0;
  color: #555;
  font-size: 1.1rem;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 0.5rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: #007bff;
}

.code-input {
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
}

.form-help {
  display: block;
  margin-top: 0.25rem;
  color: #666;
  font-size: 0.85rem;
}

.tags-preview {
  margin-top: 0.5rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag {
  background-color: #e9ecef;
  color: #495057;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.tag-remove {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  font-size: 1rem;
  line-height: 1;
  padding: 0;
}

.tag-remove:hover {
  color: #333;
}

.file-upload {
  position: relative;
}

.file-input {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

.file-upload-area {
  border: 2px dashed #ddd;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.2s;
}

.file-upload-area:hover {
  border-color: #007bff;
}

.icon-preview img {
  width: 64px;
  height: 64px;
  border-radius: 8px;
}

.upload-placeholder {
  color: #666;
}

.upload-placeholder i {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  display: block;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e0e0e0;
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
</style>
